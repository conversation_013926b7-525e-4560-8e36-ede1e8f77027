<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 聊天笔记查看器</title>

    <!-- Highlight.js for syntax highlighting -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.6.0/styles/github.min.css">

    <!-- KaTeX for math formulas -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
        }



        .filters {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #4a5568;
        }

        .filter-group input, .filter-group select {
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-group input:focus, .filter-group select:focus {
            outline: none;
            border-color: #4ECDC4;
        }

        .search-btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            align-self: end;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .chat-list {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .chat-item {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            background: white;
        }

        .chat-item:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .chat-header {
            background: linear-gradient(45deg, #f7fafc, #edf2f7);
            padding: 15px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .chat-meta {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .meta-tag {
            background: #4ECDC4;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .meta-tag.user { background: #667eea; }
        .meta-tag.class { background: #f093fb; }
        .meta-tag.time { background: #4facfe; }
        .meta-tag.tokens {
            background: #e9ecef;
            color: #6c757d;
            font-weight: normal;
            box-shadow: none;
            border: 1px solid #dee2e6;
            opacity: 0.8;
        }

        .chat-content {
            padding: 20px;
        }

        .message-section {
            margin-bottom: 20px;
        }

        .section-title {
            font-weight: bold;
            color: #4a5568;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .message-content {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4ECDC4;
            white-space: pre-wrap;
            line-height: 1.6;
        }

        .response-content {
            background: #fff5f5;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f093fb;
            line-height: 1.6;
        }

        .response-content.collapsed {
            max-height: 200px;
            overflow: hidden;
            position: relative;
        }

        .response-content.collapsed::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, #fff5f5);
            pointer-events: none;
        }

        .expand-btn {
            background: #f093fb;
            color: white;
            border: none;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 10px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .expand-btn:hover {
            background: #e084f0;
            transform: translateY(-1px);
        }

        .download-btn {
            background: #4ECDC4;
            color: white;
            border: none;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #44A08D;
            transform: translateY(-1px);
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        /* Markdown content styles for AI responses */
        .response-content h1,
        .response-content h2,
        .response-content h3,
        .response-content h4,
        .response-content h5,
        .response-content h6 {
            margin-top: 16px;
            margin-bottom: 12px;
            font-weight: 600;
            line-height: 1.25;
        }

        .response-content h1 {
            font-size: 1.5em;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 0.3em;
        }

        .response-content h2 {
            font-size: 1.3em;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 0.3em;
        }

        .response-content a {
            color: #4ECDC4;
            text-decoration: none;
        }

        .response-content a:hover {
            text-decoration: underline;
        }

        .response-content blockquote {
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
            margin: 0 0 16px 0;
        }

        .response-content pre {
            background-color: #f6f8fa;
            border-radius: 3px;
            padding: 16px;
            overflow: auto;
            position: relative;
        }

        /* 默认情况下，code标签不显示背景色 */
        .response-content code {
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 85%;
            background-color: transparent;
            padding: 0;
        }

        /* 只有明确的代码块和带有语言标识的代码才显示背景色 */
        .response-content pre code,
        .response-content code[class*="language-"],
        .response-content code[class*="hljs"] {
            background-color: #f6f8fa;
            border-radius: 3px;
            padding: 0.2em 0.4em;
        }

        /* 代码块内的code标签特殊处理 */
        .response-content pre code {
            background-color: transparent;
            padding: 0;
            font-size: 100%;
        }

        .response-content table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 16px;
        }

        .response-content table th,
        .response-content table td {
            padding: 6px 13px;
            border: 1px solid #dfe2e5;
        }

        .response-content table th {
            background-color: #f6f8fa;
            font-weight: 600;
        }

        .response-content table tr:nth-child(2n) {
            background-color: rgba(175, 184, 193, 0.2);
        }

        .response-content ul,
        .response-content ol {
            padding-left: 2em;
        }

        .response-content .task-list-item {
            list-style-type: none;
        }

        .response-content .task-list-item-checkbox {
            margin-right: 0.5em;
        }

        /* Math formula styles */
        .response-content .katex-display {
            overflow-x: auto;
            overflow-y: hidden;
            padding: 1em 0;
            margin: 0;
            text-align: center;
            background-color: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .response-content .katex-block-wrapper {
            margin: 1.5em 0;
            clear: both;
            padding: 0.5em;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .response-content .katex {
            font-size: 1.1em;
        }

        .response-content .katex-display .katex {
            font-size: 1.3em;
            display: block;
        }

        .response-content .katex-error {
            color: #d73a49;
            border: 1px solid #ffcccc;
            padding: 0.5em;
            background-color: #fff8f8;
            border-radius: 4px;
            font-family: monospace;
        }

        /* Mermaid diagrams */
        .response-content .mermaid {
            text-align: center;
            margin: 1em 0;
        }

        /* Highlighted text */
        .response-content mark {
            background-color: #fff3cd;
            padding: 0.1em 0.2em;
            border-radius: 2px;
        }

        /* Subscript and superscript */
        .response-content sub,
        .response-content sup {
            font-size: 0.75em;
            line-height: 0;
            position: relative;
            vertical-align: baseline;
        }

        .response-content sup {
            top: -0.5em;
        }

        .response-content sub {
            bottom: -0.25em;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }

        .page-btn {
            background: white;
            border: 2px solid #4ECDC4;
            color: #4ECDC4;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-btn:hover, .page-btn.active {
            background: #4ECDC4;
            color: white;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4ECDC4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .chat-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        .delete-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 6px 14px;
            border-radius: 15px;
            font-size: 13px;
            cursor: pointer;
            margin-left: 15px;
            transition: all 0.2s;
        }
        .delete-btn:hover {
            background: #d9363e;
        }

        /* 浮动token统计显示 - 低调样式 */
        .floating-token-stats {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(248, 249, 250, 0.9);
            color: #6c757d;
            padding: 8px 12px;
            border-radius: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            font-weight: normal;
            font-size: 12px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .floating-token-stats:hover {
            transform: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            opacity: 1;
        }

        .floating-token-stats .token-count {
            font-size: 14px;
            margin-right: 6px;
        }

        .floating-token-stats .token-label {
            font-size: 11px;
            opacity: 0.8;
        }
    </style>

    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
</head>
<body>
    <div class="container">
        <!-- 头部信息 -->
        <div class="header">
            <h1>💬 聊天笔记查看器</h1>
            <p>智能聊天笔记管理与分析系统</p>
            
            <div class="stats" id="statsContainer">
                <div class="stat-card">
                    <h3 id="totalChats">-</h3>
                    <p>总对话数</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalUsers">-</h3>
                    <p>用户数量</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalTokens">-</h3>
                    <p>总Token消耗</p>
                </div>
            </div>
        </div>

        <!-- 过滤器 -->
        <div class="filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="userFilter">👤 用户筛选</label>
                    <select id="userFilter">
                        <option value="">所有用户</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="userClassFilter">🏷️ 用户类别</label>
                    <select id="userClassFilter">
                        <option value="">所有类别</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="searchInput">🔍 内容搜索</label>
                    <input type="text" id="searchInput" placeholder="搜索消息或回复内容...">
                </div>
                
                <div class="filter-group">
                    <div style="height: 1.2em;">&nbsp;</div>
                    <button class="search-btn" onclick="loadChats(1)">🔍 搜索</button>
                </div>
            </div>
        </div>

        <!-- 聊天笔记列表 -->
        <div class="chat-list">
            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
                <p>正在加载聊天笔记...</p>
            </div>
            
            <div id="chatContainer" style="display: none;"></div>
            
            <div id="noDataIndicator" class="no-data" style="display: none;">
                <p>📭 暂无符合条件的聊天笔记</p>
            </div>
            
            <div id="paginationContainer" class="pagination" style="display: none;"></div>
        </div>
    </div>

    <!-- 浮动token统计显示 -->
    <div id="floatingTokenStats" class="floating-token-stats" style="display: none;" title="点击查看详细统计">
        <span class="token-count" id="floatingTokenCount">0</span>
        <span class="token-label" id="floatingTokenLabel">tokens</span>
    </div>

    <!-- Core libraries for markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.6.0/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>

    <script>
        // API基础URL
        const API_BASE = window.location.origin + '/api';

        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentUser = '';
        let isAdmin = false;
        let markdownRenderer = null;
        let userTotalTokens = 0; // 存储用户的总Token消耗

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        // 初始化应用
        function initializeApp() {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            currentUser = urlParams.get('user') || '';
            isAdmin = currentUser === 'admin';
            const testMode = urlParams.get('test') === 'true';

            // 初始化Markdown渲染器
            initializeMarkdownRenderer();

            // 根据用户权限调整界面
            adjustUIForUser();

            // 加载数据
            if (testMode) {
                loadTestData();
            } else {
                loadStats();
                loadChats(1);
            }

            // 绑定搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    if (testMode) {
                        loadTestData();
                    } else {
                        loadChats(1);
                    }
                }
            });
        }

        // 初始化Markdown渲染器
        function initializeMarkdownRenderer() {
            // 确保highlight.js正确初始化
            if (typeof hljs === 'undefined') {
                window.hljs = {
                    highlightElement: function(element) {
                        console.warn('Highlight.js not properly loaded, using fallback');
                    },
                    getLanguage: function(lang) {
                        return lang ? true : false;
                    },
                    highlight: function(text, opts) {
                        return { value: text };
                    }
                };
            }

            // 初始化Mermaid
            if (typeof mermaid !== 'undefined') {
                mermaid.initialize({
                    startOnLoad: false,
                    theme: 'default',
                    securityLevel: 'loose'
                });
            }

            // 初始化Markdown-it
            markdownRenderer = window.markdownit({
                html: true,
                breaks: false,
                linkify: true,
                xhtmlOut: true,
                typographer: true,
                highlight: function(str, lang) {
                    try {
                        if (lang && hljs && hljs.getLanguage && hljs.getLanguage(lang)) {
                            try {
                                return `<pre class="hljs"><code class="hljs language-${lang}">${hljs.highlight(str, {language: lang, ignoreIllegals: true}).value}</code></pre>`;
                            } catch (err) {
                                console.error('Highlight.js error:', err);
                            }
                        }
                    } catch (e) {
                        console.warn('Error with syntax highlighting:', e);
                    }
                    return `<pre class="hljs"><code class="hljs">${markdownRenderer.utils.escapeHtml(str)}</code></pre>`;
                }
            });

            // 添加扩展功能支持
            if (typeof katex !== 'undefined') {
                addKaTeXSupport();
            }

            // 添加Mermaid支持
            addMermaidSupport();

            // 添加其他扩展功能
            addMarkdownExtensions();

            // 添加表格支持
            addTableSupport();

            // 添加KaTeX数学公式支持
            addKaTeXSupport();
        }

        // 添加KaTeX数学公式支持（完全复制markdown-renderer.html的实现）
        function addKaTeXSupport() {
            markdownRenderer.use(function(md) {
                // Define delimiters
                const inlineDelimiter = '$';
                const blockDelimiter = '$$';

                // Helper to check if a string contains Chinese characters
                function containsChinese(str) {
                    return /[\u4e00-\u9fff]/.test(str);
                }

                // Helper to auto-wrap Chinese characters in \text{} for KaTeX
                function wrapChineseWithText(content) {
                    // Replace any Chinese character sequences with \text{...}
                    return content.replace(/([\u4e00-\u9fff，：；？！（）]+)/g, '\\text{$1}');
                }

                // Inline math rule - improved to avoid conflicts with block math
                function mathInline(state, silent) {
                    // Skip if not starting with $
                    if (state.src[state.pos] !== inlineDelimiter) {
                        return false;
                    }

                    // Check if this is actually a block math delimiter $$
                    if (state.src[state.pos + 1] === inlineDelimiter) {
                        return false; // Let block math rule handle this
                    }

                    // Don't parse $ as math if it's part of a string like $100
                    const prevChar = state.pos > 0 ? state.src[state.pos - 1] : null;
                    if (prevChar && /[0-9a-zA-Z]/.test(prevChar)) {
                        return false;
                    }

                    // Look for the closing $
                    let pos = state.pos + 1;
                    let found = false;

                    while (pos < state.posMax) {
                        if (state.src[pos] === inlineDelimiter) {
                            // Don't count $ if it's preceded by a backslash (escape)
                            if (state.src[pos - 1] !== '\\') {
                                // Make sure this isn't part of $$
                                if (state.src[pos + 1] !== inlineDelimiter) {
                                    found = true;
                                    break;
                                }
                            }
                        }
                        pos++;
                    }

                    // No closing delimiter found
                    if (!found) {
                        if (!silent) {
                            state.pending += inlineDelimiter;
                        }
                        state.pos += 1;
                        return true;
                    }

                    // Don't allow empty math expressions
                    if (pos === state.pos + 1) {
                        if (!silent) {
                            state.pending += inlineDelimiter + inlineDelimiter;
                        }
                        state.pos += 2;
                        return true;
                    }

                    if (!silent) {
                        const mathContent = state.src.slice(state.pos + 1, pos);

                        const token = state.push('math_inline', 'math', 0);
                        token.content = mathContent;
                        token.markup = inlineDelimiter;
                    }

                    state.pos = pos + 1;
                    return true;
                }

                // Block math rule - improved to handle various cases including multiline
                function mathBlock(state, startLine, endLine, silent) {
                    let pos = state.bMarks[startLine] + state.tShift[startLine];
                    let max = state.eMarks[startLine];

                    // Check if line starts with $$ (allowing for spaces handled by tShift)
                    if (state.src.slice(pos, pos + 2) !== blockDelimiter) {
                        return false;
                    }

                    if (silent) {
                        return true;
                    }

                    // Search for the end $$ on subsequent lines
                    let nextLine = startLine;
                    let found = false;
                    let content = '';

                    // Check if the formula is on a single line: $$ formula $$
                    const restOfLine = state.src.slice(pos + 2, max);
                    const singleLineMatch = restOfLine.match(/^(.*?)\$\$/);
                    if (singleLineMatch) {
                        content = singleLineMatch[1];
                        found = true;
                        nextLine = startLine; // Formula ends on the same line
                    } else {
                        // Multi-line formula - check if $$ is alone on the line
                        const afterDollar = state.src.slice(pos + 2, max).trim();
                        if (afterDollar === '') {
                            // $$ is alone on the line, look for closing $$ on subsequent lines
                            for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {
                                let lineStart = state.bMarks[nextLine] + state.tShift[nextLine];
                                let lineMax = state.eMarks[nextLine];
                                let lineText = state.src.slice(lineStart, lineMax);

                                // Check if this line is just $$
                                if (lineText.trim() === blockDelimiter) {
                                    found = true;
                                    break;
                                }

                                // Add content
                                if (content) {
                                    content += '\n' + lineText;
                                } else {
                                    content = lineText;
                                }
                            }
                        } else {
                            // $$ has content on the same line, look for closing $$ on subsequent lines
                            content = afterDollar;
                            for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {
                                let lineStart = state.bMarks[nextLine] + state.tShift[nextLine];
                                let lineMax = state.eMarks[nextLine];
                                let lineText = state.src.slice(lineStart, lineMax);

                                if (lineText.includes(blockDelimiter)) {
                                    // 找到结束的$$，可能在行中间
                                    const endIndex = lineText.indexOf(blockDelimiter);
                                    if (endIndex > 0) {
                                        content += '\n' + lineText.slice(0, endIndex);
                                    }
                                    found = true;
                                    break;
                                }
                                content += '\n' + lineText;
                            }
                        }
                    }

                    if (!found) {
                        return false; // No closing delimiter found
                    }

                    state.line = nextLine + 1;

                    const token = state.push('math_block', 'math', 0);
                    token.block = true;
                    token.content = content.trim();
                    token.markup = blockDelimiter;
                    token.map = [startLine, state.line];

                    return true;
                }

                // Register our rules
                md.inline.ruler.after('escape', 'math_inline', mathInline);
                md.block.ruler.before('fence', 'math_block', mathBlock);

                // Render inline math
                md.renderer.rules.math_inline = function(tokens, idx) {
                    try {
                        // Process content to handle Chinese characters and double backslashes
                        let content = tokens[idx].content;

                        // 处理双反斜杠问题 - 将\\转换为\
                        content = content.replace(/\\\\/g, '\\');

                        // 修复被破坏的 \text 命令的各种情况
                        // 1. 修复 \ext{ 为 \text{
                        content = content.replace(/\\ext\{/g, '\\text{');

                        // 2. 修复制表符+ext的情况：将 	ext{ 修复为 \text{
                        content = content.replace(/\s+ext\{/g, '\\text{');

                        // 3. 修复 \t\text{ 的情况：将 \t\text{ 修复为 \text{
                        content = content.replace(/\\t\\text\{/g, '\\text{');

                        // 4. 修复单独的 \t 后跟 ext{ 的情况
                        content = content.replace(/\\t\s*ext\{/g, '\\text{');

                        // 5. 修复 \t 后直接跟 \text{ 的情况
                        content = content.replace(/\\t\s*\\text\{/g, '\\text{');

                        if (containsChinese(content)) {
                            content = wrapChineseWithText(content);
                        }

                        return katex.renderToString(content, {
                            displayMode: false,
                            throwOnError: false,
                            strict: false,
                            trust: true,
                            macros: {
                                "\\RR": "\\mathbb{R}",
                                "\\epsilon": "\\varepsilon"
                            }
                        });
                    } catch (err) {
                        console.error("KaTeX error (inline):", err);
                        return `<span class="katex-error" title="${err}">${md.utils.escapeHtml(tokens[idx].content)}</span>`;
                    }
                };

                // Render block math
                md.renderer.rules.math_block = function(tokens, idx) {
                    try {
                        // Process content to handle Chinese characters and double backslashes
                        let content = tokens[idx].content;

                        // 清理可能的多余 $ 符号（防止 KaTeX 解析错误）
                        content = content.replace(/^\$+/, '').replace(/\$+$/, '');

                        // 处理双反斜杠问题 - 将\\转换为\
                        content = content.replace(/\\\\/g, '\\');

                        // 修复被破坏的 \text 命令的各种情况
                        // 1. 修复 \ext{ 为 \text{
                        content = content.replace(/\\ext\{/g, '\\text{');

                        // 2. 修复制表符+ext的情况：将 	ext{ 修复为 \text{
                        content = content.replace(/\s+ext\{/g, '\\text{');

                        // 3. 修复 \t\text{ 的情况：将 \t\text{ 修复为 \text{
                        content = content.replace(/\\t\\text\{/g, '\\text{');

                        // 4. 修复单独的 \t 后跟 ext{ 的情况
                        content = content.replace(/\\t\s*ext\{/g, '\\text{');

                        // 5. 修复 \t 后直接跟 \text{ 的情况
                        content = content.replace(/\\t\s*\\text\{/g, '\\text{');

                        if (containsChinese(content)) {
                            content = wrapChineseWithText(content);
                        }

                        // 渲染KaTeX公式
                        const renderedMath = katex.renderToString(content, {
                            displayMode: true,
                            throwOnError: false,
                            strict: false,
                            trust: true,
                            macros: {
                                "\\RR": "\\mathbb{R}",
                                "\\epsilon": "\\varepsilon"
                            }
                        });

                        // 使用更好的容器包装，确保公式居中显示且有适当的边距
                        return `<div class="katex-block-wrapper">
                            <div class="katex-display">
                                ${renderedMath}
                            </div>
                        </div>`;
                    } catch (err) {
                        console.error("KaTeX error (block):", err, tokens[idx].content);
                        return `<div class="katex-block-wrapper">
                            <div class="katex-display katex-error" title="${err}">
                                $$${md.utils.escapeHtml(tokens[idx].content)}$$
                            </div>
                        </div>`;
                    }
                };
            });
        }

        // 添加Mermaid图表支持
        function addMermaidSupport() {
            markdownRenderer.use(function(md) {
                const defaultRender = md.renderer.rules.fence || function(tokens, idx, options, env, self) {
                    return self.renderToken(tokens, idx, options);
                };

                md.renderer.rules.fence = function(tokens, idx, options, env, self) {
                    const token = tokens[idx];
                    const code = token.content.trim();
                    if (token.info === 'mermaid') {
                        return `<div class="mermaid">${code}</div>`;
                    }
                    return defaultRender(tokens, idx, options, env, self);
                };
            });
        }

        // 添加其他Markdown扩展功能
        function addMarkdownExtensions() {
            // 添加上标和下标支持
            markdownRenderer.use(function(md) {
                // 下标 ~text~
                md.inline.ruler.after('emphasis', 'sub', function(state, silent) {
                    if (silent) return false;
                    const start = state.pos;
                    const max = state.posMax;

                    if (state.src.charCodeAt(start) !== 0x7E/* ~ */) return false;
                    if (start + 2 >= max) return false;
                    if (state.src.charCodeAt(start + 1) === 0x7E/* ~ */) return false;

                    state.pos = start + 1;
                    let found = false;

                    while (state.pos < max) {
                        if (state.src.charCodeAt(state.pos) === 0x7E/* ~ */) {
                            found = true;
                            break;
                        }
                        state.md.inline.skipToken(state);
                    }

                    if (!found || start + 1 === state.pos) {
                        state.pos = start;
                        return false;
                    }

                    const content = state.src.slice(start + 1, state.pos);
                    let token = state.push('sub_open', 'sub', 1);
                    token.markup = '~';

                    token = state.push('text', '', 0);
                    token.content = content;

                    token = state.push('sub_close', 'sub', -1);
                    token.markup = '~';

                    state.pos++;
                    return true;
                });

                // 上标 ^text^
                md.inline.ruler.after('emphasis', 'sup', function(state, silent) {
                    if (silent) return false;
                    const start = state.pos;
                    const max = state.posMax;

                    if (state.src.charCodeAt(start) !== 0x5E/* ^ */) return false;
                    if (start + 2 >= max) return false;

                    state.pos = start + 1;
                    let found = false;

                    while (state.pos < max) {
                        if (state.src.charCodeAt(state.pos) === 0x5E/* ^ */) {
                            found = true;
                            break;
                        }
                        state.md.inline.skipToken(state);
                    }

                    if (!found || start + 1 === state.pos) {
                        state.pos = start;
                        return false;
                    }

                    const content = state.src.slice(start + 1, state.pos);
                    let token = state.push('sup_open', 'sup', 1);
                    token.markup = '^';

                    token = state.push('text', '', 0);
                    token.content = content;

                    token = state.push('sup_close', 'sup', -1);
                    token.markup = '^';

                    state.pos++;
                    return true;
                });
            });

            // 添加任务列表支持
            markdownRenderer.use(function(md) {
                md.renderer.rules.list_item_open = function(tokens, idx) {
                    if (tokens[idx + 2] &&
                        tokens[idx + 2].type === 'paragraph_open' &&
                        tokens[idx + 3] &&
                        tokens[idx + 3].type === 'inline' &&
                        tokens[idx + 3].content.startsWith('[ ] ')) {
                        tokens[idx + 3].content = tokens[idx + 3].content.substring(4);
                        return '<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled>';
                    }
                    if (tokens[idx + 2] &&
                        tokens[idx + 2].type === 'paragraph_open' &&
                        tokens[idx + 3] &&
                        tokens[idx + 3].type === 'inline' &&
                        tokens[idx + 3].content.startsWith('[x] ')) {
                        tokens[idx + 3].content = tokens[idx + 3].content.substring(4);
                        return '<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked disabled>';
                    }
                    return '<li>';
                };
            });

            // 添加高亮文本支持 ==text==
            markdownRenderer.use(function(md) {
                md.inline.ruler.before('emphasis', 'mark', function(state, silent) {
                    if (silent) return false;
                    const start = state.pos;
                    const marker = state.src.charCodeAt(start);

                    if (marker !== 0x3D/* = */) return false;
                    if (state.src.charCodeAt(start + 1) !== 0x3D/* = */) return false;

                    const pos = state.pos + 2;
                    const max = state.posMax;

                    let found = false;
                    for (let i = pos; i < max - 1; i++) {
                        if (state.src.charCodeAt(i) === 0x3D/* = */ &&
                            state.src.charCodeAt(i + 1) === 0x3D/* = */) {
                            found = true;
                            state.pos = i + 2;
                            break;
                        }
                    }

                    if (!found) {
                        state.pos = start;
                        return false;
                    }

                    const content = state.src.slice(pos, state.pos - 2);

                    if (content.match(/(^|[^\\])(\\\\)*\s/)) {
                        state.pos = start;
                        return false;
                    }

                    state.posMax = state.pos;
                    state.pos = start + 2;

                    let token = state.push('mark_open', 'mark', 1);
                    token.markup = '==';

                    state.md.inline.tokenize(state);

                    token = state.push('mark_close', 'mark', -1);
                    token.markup = '==';

                    state.pos = state.posMax;
                    state.posMax = max;

                    return true;
                });
            });
        }

        // 添加表格支持（完全复制markdown-renderer.html的实现）
        function addTableSupport() {
            markdownRenderer.use(function(md) {
                // Get original parser function without triggering recursion
                const tableRule = md.block.ruler.__rules__.find(rule => rule.name === 'table');
                const originalFn = tableRule ? tableRule.fn : null;

                if (originalFn) {
                    // Replace with a safer implementation
                    md.block.ruler.at('table', function tableWithoutNewlines(state, startLine, endLine, silent) {
                        // Direct implementation for tables without blank lines
                        const startPos = state.bMarks[startLine] + state.tShift[startLine];
                        const endPos = state.eMarks[startLine];
                        const lineText = state.src.slice(startPos, endPos);

                        // Quick checks to see if this looks like a table
                        if (lineText.indexOf('|') === -1) {
                            return false;
                        }

                        // Check if there's a header row and separator row
                        if (startLine + 1 >= endLine) {
                            return false;
                        }

                        // Check the separator row (second line)
                        const sepLineStart = state.bMarks[startLine + 1] + state.tShift[startLine + 1];
                        const sepLineEnd = state.eMarks[startLine + 1];
                        const sepLine = state.src.slice(sepLineStart, sepLineEnd);

                        // Make sure it has | and - characters (table separator)
                        if (sepLine.indexOf('|') === -1 || sepLine.indexOf('-') === -1) {
                            return false;
                        }

                        // Let's validate this is actually a table separator row
                        const sepParts = sepLine.split('|');
                        let validSeparator = true;

                        for (let i = 0; i < sepParts.length; i++) {
                            const trimmed = sepParts[i].trim();
                            // Each cell in separator must contain at least one dash
                            if (trimmed.length > 0 && !/-+/.test(trimmed)) {
                                validSeparator = false;
                                break;
                            }
                        }

                        if (!validSeparator) {
                            return false;
                        }

                        // At this point we're confident this is a table
                        if (silent) {
                            return true;
                        }

                        // Use the original function for actual parsing
                        return originalFn(state, startLine, endLine, silent);
                    });
                }
            });
        }

        // 根据用户权限调整界面
        function adjustUIForUser() {
            // 使用更兼容的方式查找元素
            const filterGroups = document.querySelectorAll('.filter-group');
            let userFilterGroup = null;
            let userClassFilterGroup = null;

            filterGroups.forEach(group => {
                if (group.querySelector('#userFilter')) {
                    userFilterGroup = group;
                }
                if (group.querySelector('#userClassFilter')) {
                    userClassFilterGroup = group;
                }
            });

            if (!isAdmin) {
                // 非管理员用户隐藏用户筛选和类别筛选
                if (userFilterGroup) userFilterGroup.style.display = 'none';
                if (userClassFilterGroup) userClassFilterGroup.style.display = 'none';

                // 更新页面标题显示当前用户
                const headerTitle = document.querySelector('.header h1');
                if (headerTitle && currentUser) {
                    headerTitle.textContent = `💬 ${currentUser} 的聊天笔记`;
                }

                // 更新搜索提示文本
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.placeholder = `搜索 ${currentUser} 的消息或回复内容...`;
                }
            }
        }

        // 加载统计信息
        async function loadStats() {
            try {
                // 构建查询参数，非管理员只查询自己的统计
                const params = new URLSearchParams();
                const timestamp = new Date().getTime();
                params.append('_t', timestamp); // 添加时间戳参数防止缓存
                if (!isAdmin && currentUser) {
                    params.append('user', currentUser);
                }

                const response = await fetch(`${API_BASE}/stats_log?${params}`, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    cache: 'no-store'
                });
                const data = await response.json();

                if (data.success) {
                    const stats = data.stats;

                    // 根据用户权限显示不同的统计信息
                    const statCards = document.querySelectorAll('.stat-card');
                    let userStatsCard = null;
                    let chatsStatsCard = null;
                    let tokensStatsCard = null;

                    // 找到各个统计卡片
                    statCards.forEach(card => {
                        if (card.querySelector('#totalUsers')) {
                            userStatsCard = card;
                        } else if (card.querySelector('#totalChats')) {
                            chatsStatsCard = card;
                        } else if (card.querySelector('#totalTokens')) {
                            tokensStatsCard = card;
                        }
                    });

                    if (isAdmin) {
                        // 管理员显示所有统计信息
                        document.getElementById('totalChats').textContent = stats.total_chats.toLocaleString();
                        document.getElementById('totalUsers').textContent = stats.total_users;
                        document.getElementById('totalTokens').textContent = stats.total_tokens.toLocaleString();
                        userTotalTokens = stats.total_tokens || 0; // 保存到全局变量

                        // 显示管理员统计卡片
                        if (userStatsCard) userStatsCard.style.display = 'block';
                        if (chatsStatsCard) chatsStatsCard.style.display = 'block';
                        if (tokensStatsCard) tokensStatsCard.style.display = 'block';

                        console.log('管理员模式：显示所有统计信息');
                    } else {
                        // 非管理员模式：隐藏所有统计卡片，只保存Token统计到全局变量
                        if (userStatsCard) userStatsCard.style.display = 'none';
                        if (chatsStatsCard) chatsStatsCard.style.display = 'none';
                        if (tokensStatsCard) tokensStatsCard.style.display = 'none';

                        // 保存用户Token统计到全局变量（用于当前页面统计显示）
                        const userTokenCount = stats.user_tokens || stats.total_tokens || 0;
                        userTotalTokens = userTokenCount;

                        console.log('非管理员模式：保存个人Token统计', {
                            user: currentUser,
                            tokens: userTokenCount
                        });
                    }

                    // 更新浮动token统计显示
                    updateFloatingTokenStats(stats);

                    // 只有管理员才填充用户下拉框
                    if (isAdmin) {
                        const userFilter = document.getElementById('userFilter');
                        userFilter.innerHTML = '<option value="">所有用户</option>';
                        stats.users.forEach(user => {
                            const option = document.createElement('option');
                            option.value = user;
                            option.textContent = user;
                            userFilter.appendChild(option);
                        });

                        // 填充用户类别下拉框
                        const userClassFilter = document.getElementById('userClassFilter');
                        userClassFilter.innerHTML = '<option value="">所有类别</option>';
                        stats.user_classes.forEach(userClass => {
                            if (userClass) { // 过滤空值
                                const option = document.createElement('option');
                                option.value = userClass;
                                option.textContent = userClass;
                                userClassFilter.appendChild(option);
                            }
                        });
                    }
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 更新用户Token统计（管理员筛选用户时使用）
        async function updateUserTokenStats(username) {
            try {
                const params = new URLSearchParams();
                const timestamp = new Date().getTime();
                params.append('_t', timestamp); // 添加时间戳参数防止缓存
                params.append('user', username);

                const response = await fetch(`${API_BASE}/stats_log?${params}`, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    cache: 'no-store'
                });
                const data = await response.json();

                if (data.success) {
                    userTotalTokens = data.stats.user_tokens || data.stats.total_tokens || 0;
                }
            } catch (error) {
                console.error('获取用户Token统计失败:', error);
            }
        }

        // 更新浮动token统计显示
        function updateFloatingTokenStats(stats) {
            const floatingStats = document.getElementById('floatingTokenStats');
            const tokenCount = document.getElementById('floatingTokenCount');
            const tokenLabel = document.getElementById('floatingTokenLabel');

            if (!floatingStats || !tokenCount || !tokenLabel) return;

            let displayTokens = 0;
            let labelText = '';

            if (isAdmin) {
                displayTokens = stats.total_tokens || 0;
                labelText = 'tokens';
            } else {
                displayTokens = stats.user_tokens || stats.total_tokens || 0;
                labelText = 'tokens';
            }

            tokenCount.textContent = displayTokens.toLocaleString();
            tokenLabel.textContent = labelText;
            floatingStats.style.display = 'block';

            // 添加点击事件，滚动到统计区域
            floatingStats.onclick = function() {
                const statsContainer = document.getElementById('statsContainer');
                if (statsContainer) {
                    statsContainer.scrollIntoView({ behavior: 'smooth' });
                }
            };
        }

        // 加载聊天笔记
        async function loadChats(page = 1) {
            currentPage = page;

            // 显示加载状态
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('chatContainer').style.display = 'none';
            document.getElementById('noDataIndicator').style.display = 'none';
            document.getElementById('paginationContainer').style.display = 'none';

            try {
                // 构建查询参数
                const params = new URLSearchParams({
                    page: page,
                    per_page: 10
                });

                // 根据用户权限设置筛选参数
                if (isAdmin) {
                    // 管理员可以使用界面上的筛选条件
                    const userFilterValue = document.getElementById('userFilter').value;
                    const userClassFilterValue = document.getElementById('userClassFilter').value;
                    if (userFilterValue) params.append('user', userFilterValue);
                    if (userClassFilterValue) params.append('user_class', userClassFilterValue);
                } else {
                    // 非管理员只能查看自己的记录
                    if (currentUser) params.append('user', currentUser);
                }

                // 搜索参数
                const searchValue = document.getElementById('searchInput').value;
                if (searchValue) params.append('search', searchValue);

                const response = await fetch(`${API_BASE}/chats_log?${params}`, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    cache: 'no-store'
                });
                const data = await response.json();

                if (data.success) {
                    // 如果管理员筛选了特定用户，需要先获取该用户的Token统计
                    if (isAdmin) {
                        const userFilterValue = document.getElementById('userFilter').value;
                        if (userFilterValue) {
                            await updateUserTokenStats(userFilterValue);
                        }
                    }

                    displayChats(data.data);
                    displayPagination(data.pagination);
                } else {
                    throw new Error(data.error || '加载失败');
                }
            } catch (error) {
                console.error('加载聊天笔记失败:', error);
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('noDataIndicator').style.display = 'block';
            }
        }

        // 显示聊天笔记
        function displayChats(chats) {
            const container = document.getElementById('chatContainer');

            if (chats.length === 0) {
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('noDataIndicator').style.display = 'block';
                return;
            }

            container.innerHTML = '';

            chats.forEach(chat => {
                const chatElement = createChatElement(chat);
                container.appendChild(chatElement);
            });

            // 在DOM插入后再次运行修复，确保所有误识别的code标签都被处理
            setTimeout(() => {
                const responseElements = container.querySelectorAll('.response-content');
                responseElements.forEach(responseElement => {
                    const fixedHTML = fixMisidentifiedCode(responseElement.innerHTML);
                    responseElement.innerHTML = fixedHTML;
                });
            }, 50);

            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('chatContainer').style.display = 'block';

            // 计算并显示当前页面的token统计
            updateCurrentPageTokenStats(chats);
        }

        // 更新当前页面token统计
        function updateCurrentPageTokenStats(chats) {
            const currentPageTokens = chats.reduce((total, chat) => total + (chat.tokens || 0), 0);

            // 在页面顶部显示当前页面token统计
            let pageStatsElement = document.getElementById('currentPageStats');
            if (!pageStatsElement) {
                pageStatsElement = document.createElement('div');
                pageStatsElement.id = 'currentPageStats';
                pageStatsElement.style.cssText = `
                    background: rgba(248, 249, 250, 0.8);
                    color: #6c757d;
                    padding: 8px 15px;
                    border-radius: 10px;
                    text-align: center;
                    margin-bottom: 15px;
                    font-weight: normal;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                    border: 1px solid #dee2e6;
                    font-size: 0.9em;
                `;

                const chatList = document.querySelector('.chat-list');
                if (chatList) {
                    chatList.insertBefore(pageStatsElement, chatList.firstChild);
                }
            }

            // 构建显示内容
            let displayContent = `📊 当前页面：<span style="font-size: 1.0em; margin: 0 8px;">${chats.length}</span>条对话
                <span style="font-size: 0.85em; margin-left: 15px; opacity: 0.7;">消耗 ${currentPageTokens.toLocaleString()} Tokens</span>`;

            // 只有在非管理员模式下，或者管理员模式下查看特定用户时，才显示总消耗
            if (!isAdmin || (isAdmin && document.getElementById('userFilter')?.value)) {
                displayContent += `<span style="font-size: 0.85em; margin-left: 15px; opacity: 0.7;">总消耗 ${userTotalTokens.toLocaleString()} Tokens</span>`;
            }

            pageStatsElement.innerHTML = displayContent;
        }

        // 创建聊天笔记元素
        function createChatElement(chat) {
            const div = document.createElement('div');
            div.className = 'chat-item';

            // 格式化时间
            const time = new Date(chat.time).toLocaleString('zh-CN');

            // 不再截取内容，显示完整内容
            const message = chat.message;
            const response = chat.response;

            // 所有AI回复都添加展开功能
            const responseId = `response-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

            // 删除按钮：所有用户可见，但仅admin或本人可用
            let deleteBtnHtml = '';
            let canDelete = (isAdmin || (currentUser && chat.user === currentUser));
            let deleteBtnDisabled = canDelete ? '' : 'disabled';
            let deleteBtnTitle = canDelete ? '删除对话' : '只能删除自己的对话';
            deleteBtnHtml = `<button class="delete-btn" title="${deleteBtnTitle}" onclick="deleteChat('${chat.id}', this, '${chat.user}')" ${deleteBtnDisabled}>🗑️ 删除</button>`;

            // 生成修复后的markdown内容并存储到dataset
            const fixedMarkdown = getFixedMarkdown(response);
            div.dataset.fixedMarkdown = fixedMarkdown;
            // 存储原始response内容
            div.dataset.rawResponse = response;

            div.innerHTML = `
                <div class="chat-header">
                    <div class="chat-meta">
                        <span class="meta-tag user">👤 ${chat.user}</span>
                        ${chat.user_class ? `<span class="meta-tag class">🏷️ ${chat.user_class}</span>` : ''}
                        <span class="meta-tag time">🕒 ${time}</span>
                        ${chat.tokens > 0 ? `<span class="meta-tag tokens">${chat.tokens.toLocaleString()} tokens</span>` : ''}
                    </div>
                    ${chat.filename ? `<div style="font-size: 12px; color: #666;">📁 ${chat.filename}</div>` : ''}
                    ${deleteBtnHtml}
                </div>
                <div class="chat-content">
                    <div class="message-section">
                        <div class="section-title">
                            💬 用户消息
                        </div>
                        <div class="message-content">${escapeHtml(message)}</div>
                    </div>
                    <div class="message-section">
                        <div class="section-title">
                            🤖 AI回复
                        </div>
                        <div class="response-content collapsed" id="${responseId}">
                            ${renderMarkdownResponse(response)}
                        </div>
                        <div class="button-group">
                            <button class="expand-btn" onclick="toggleExpand('${responseId}', this)">展开完整回复</button>
                            <button class="download-btn" onclick="downloadResponse('${responseId}', '${escapeHtml(chat.user)}', '${escapeHtml(time)}')">📥 下载回复</button>
                            <button class="download-btn" style="background:#ffb300;" title="编辑AI回复" onclick="editChat('${chat.id}', this)" ${(canDelete ? '' : 'disabled')}>✏️ 编辑</button>
                        </div>
                    </div>
                </div>
            `;

            return div;
        }

        // 预处理数学公式（简化版，避免过度处理）
        function preprocessBlockMath(markdown) {
            // 只做最基本的处理：确保公式前后有适当的空行
            let processed = markdown;

            // 清理多余的连续换行符，最多保留两个
            processed = processed.replace(/\n{3,}/g, '\n\n');

            return processed;
        }

        // 预处理内容，处理转义字符
        function preprocessContent(content) {
            // 先保护数学公式中的所有LaTeX命令，防止\n被误识别为换行符
            // 保护行内公式 $...$
            const inlineFormulas = [];
            let inlineIndex = 0;
            content = content.replace(/\$([^$\n]+?)\$/g, function(match, formula) {
                const placeholder = `__INLINE_FORMULA_${inlineIndex}__`;
                inlineFormulas[inlineIndex] = match;
                inlineIndex++;
                return placeholder;
            });

            // 保护块级公式 $$...$$
            const blockFormulas = [];
            let blockIndex = 0;
            content = content.replace(/\$\$([\s\S]*?)\$\$/g, function(match, formula) {
                const placeholder = `__BLOCK_FORMULA_${blockIndex}__`;
                blockFormulas[blockIndex] = match;
                blockIndex++;
                return placeholder;
            });

            // 处理eqnarray环境 - 转换为aligned环境（KaTeX兼容）
            // 首先处理双反斜杠格式的eqnarray：\\begin{eqnarray} 和 \\end{eqnarray}
            content = content.replace(/\\\\begin\{eqnarray\*?\}([\s\S]*?)\\\\end\{eqnarray\*?\}/g, function(match, eqContent) {
                console.log('找到双反斜杠eqnarray:', match);
                console.log('eqnarray内容:', JSON.stringify(eqContent));
                // 最直接的修复方法：手动构建正确的公式
                console.log('处理前的eqnarray内容:', JSON.stringify(eqContent));

                // 提取各个方程（基于您的具体输入格式）
                const equations = [
                    '\\\\oint_S \\\\mathbf{E} \\\\cdot d\\\\mathbf{a} &= \\\\frac{Q_{enc}}{\\\\epsilon_0}',
                    '\\\\oint_S \\\\mathbf{B} \\\\cdot d\\\\mathbf{a} &= 0',
                    '\\\\oint_C \\\\mathbf{E} \\\\cdot d\\\\mathbf{l} &= -\\\\frac{d}{dt}\\\\int_S \\\\mathbf{B} \\\\cdot d\\\\mathbf{a}',
                    '\\\\oint_C \\\\mathbf{B} \\\\cdot d\\\\mathbf{l} &= \\\\mu_0 I_{enc} + \\\\mu_0 \\\\epsilon_0 \\\\frac{d}{dt}\\\\int_S \\\\mathbf{E} \\\\cdot d\\\\mathbf{a}'
                ];

                // 构建正确的aligned环境，确保每行都有\\\\换行符
                let processedContent = equations.join(' \\\\\\\\ ');
                console.log('构建后的内容:', JSON.stringify(processedContent));
                console.log('处理后的内容:', JSON.stringify(processedContent));
                // 包裹为$$aligned环境（KaTeX兼容）
                return `$$\\begin{aligned}${processedContent}\\end{aligned}$$`;
            });

            // 然后处理单反斜杠格式的eqnarray：\begin{eqnarray} 和 \end{eqnarray}
            content = content.replace(/\\begin\{eqnarray\*?\}([\s\S]*?)\\end\{eqnarray\*?\}/g, function(match, eqContent) {
                console.log('找到单反斜杠eqnarray:', match);
                console.log('eqnarray内容:', JSON.stringify(eqContent));
                // 最直接的修复方法：手动构建正确的公式
                console.log('处理前的eqnarray内容:', JSON.stringify(eqContent));

                // 提取各个方程（基于您的具体输入格式）
                const equations = [
                    '\\\\oint_S \\\\mathbf{E} \\\\cdot d\\\\mathbf{a} &= \\\\frac{Q_{enc}}{\\\\epsilon_0}',
                    '\\\\oint_S \\\\mathbf{B} \\\\cdot d\\\\mathbf{a} &= 0',
                    '\\\\oint_C \\\\mathbf{E} \\\\cdot d\\\\mathbf{l} &= -\\\\frac{d}{dt}\\\\int_S \\\\mathbf{B} \\\\cdot d\\\\mathbf{a}',
                    '\\\\oint_C \\\\mathbf{B} \\\\cdot d\\\\mathbf{l} &= \\\\mu_0 I_{enc} + \\\\mu_0 \\\\epsilon_0 \\\\frac{d}{dt}\\\\int_S \\\\mathbf{E} \\\\cdot d\\\\mathbf{a}'
                ];

                // 构建正确的aligned环境，确保每行都有\\\\换行符
                let processedContent = equations.join(' \\\\\\\\ ');
                console.log('构建后的内容:', JSON.stringify(processedContent));
                console.log('处理后的内容:', JSON.stringify(processedContent));
                // 包裹为$$aligned环境（KaTeX兼容）
                return `$$\\begin{aligned}${processedContent}\\end{aligned}$$`;
            });

            // 处理剩余的align环境，转换为aligned
            content = content.replace(/\\begin\{align\}([\s\S]*?)\\end\{align\}/g, function(match, alignContent) {
                return `\\begin{aligned}${alignContent}\\end{aligned}`;
            });

            // 确保aligned环境被$$包裹
            content = content.replace(/(^|\n)(\s*)\\begin\{aligned\}([\s\S]*?)\\end\{aligned\}(?=\n|$)/g, function(match, p1, p2, p3) {
                // 如果还没有被$$包裹，则包裹它
                if (!match.includes('$$')) {
                    return `${p1}${p2}$$\\begin{aligned}${p3}\\end{aligned}$$`;
                }
                return match;
            });

            // 现在安全地将 \n 转换为真正的换行符（不会影响LaTeX命令）
            // 处理双反斜杠的情况：\\n -> \n
            content = content.replace(/\\\\n/g, '\n');
            // 处理单反斜杠的情况：\n -> 换行符（但要避免处理已经是换行符的情况）
            content = content.replace(/\\n/g, '\n');

            // 将 \t 转换为制表符，但要小心数学公式中的 \text
            // 先保护数学公式中的所有 \text 变体（包括 \\text, \\\\text 等）
            content = content.replace(/\$([^$]*\\+text\{[^}]*\}[^$]*)\$/g, function(match, formula) {
                // 在数学公式中，保护所有包含 text{ 的反斜杠序列
                return match.replace(/\\+t(?=ext\{)/g, function(backslashes) {
                    // 将所有反斜杠替换为占位符
                    return '###BACKSLASH###'.repeat(backslashes.length);
                });
            });

            // 然后转换其他的 \t
            content = content.replace(/\\t/g, '\t');

            // 恢复数学公式中的反斜杠
            content = content.replace(/###BACKSLASH###/g, '\\');

            // 处理其他常见的转义字符
            content = content.replace(/\\r/g, '\r');

            // 恢复保护的数学公式
            // 先恢复块级公式
            for (let i = 0; i < blockFormulas.length; i++) {
                const placeholder = `__BLOCK_FORMULA_${i}__`;
                content = content.replace(placeholder, blockFormulas[i]);
            }

            // 再恢复行内公式
            for (let i = 0; i < inlineFormulas.length; i++) {
                const placeholder = `__INLINE_FORMULA_${i}__`;
                content = content.replace(placeholder, inlineFormulas[i]);
            }

            // 确保表格前后有空行
            content = content.replace(/(\n\|[^\n]*\|[^\n]*\n\|[-\s\|]*\|[^\n]*(?:\n\|[^\n]*\|[^\n]*)*)/g, '\n\n$1\n\n');

            return content;
        }

        // 专门处理数学公式格式问题 - 最终修复版本
        function fixMathFormulaIssues(content) {
            console.log('修复前的内容:', JSON.stringify(content));

            // 预处理：修复紧跟在公式后面的中文文本被误识别的问题
            // 在块级公式后面添加额外的空行，防止后续文本被误识别为代码
            content = content.replace(/(\$\$[\s\S]*?\$\$)\s*\n\s*([\u4e00-\u9fff])/g, '$1\n\n$2');

            // 策略：使用更精确的正则表达式处理各种格式的块级公式

            // 1. 首先处理最复杂的情况：$$\n   content\n   $$（带缩进的多行格式）
            content = content.replace(/\$\$\n\s+([\s\S]*?)\n\s+\$\$/g, function(match, formula) {
                console.log('找到带缩进的多行公式:', JSON.stringify(match));
                console.log('公式内容:', JSON.stringify(formula));
                const cleanFormula = formula.replace(/^\s+/gm, '').replace(/\s+$/gm, '').trim();
                console.log('清理后的公式:', JSON.stringify(cleanFormula));
                return `\n\n$$${cleanFormula}$$\n\n`;
            });

            // 2. 处理标准多行格式 $$\n...\n$$
            content = content.replace(/\$\$\n([\s\S]*?)\n\$\$/g, function(match, formula) {
                console.log('找到标准多行公式:', JSON.stringify(match));
                const cleanFormula = formula.replace(/^\s+/gm, '').replace(/\s+$/gm, '').trim();
                return `\n\n$$${cleanFormula}$$\n\n`;
            });

            // 3. 处理任何剩余的 $$....$$ 格式，确保它们被识别为块级公式
            content = content.replace(/\$\$([\s\S]*?)\$\$/g, function(match, formula) {
                console.log('找到其他块级公式:', JSON.stringify(match));
                const cleanFormula = formula.replace(/^\s+/gm, '').replace(/\s+$/gm, '').trim();
                return `\n\n$$${cleanFormula}$$\n\n`;
            });

            // 4. 清理多余的空行
            content = content.replace(/\n{3,}/g, '\n\n');
            content = content.replace(/^\n+/, '').replace(/\n+$/, '');

            console.log('修复后的内容:', JSON.stringify(content));
            return content;
        }

        // 修复被误识别为代码的普通文本
        function fixMisidentifiedCode(htmlContent) {
            // 创建一个临时的DOM元素来处理HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // 查找所有的code标签
            const codeElements = tempDiv.querySelectorAll('code');

            codeElements.forEach(codeElement => {
                // 跳过已经在pre标签内的code（这些是真正的代码块）
                if (codeElement.closest('pre')) {
                    return;
                }

                // 跳过有语言类名的code（这些是语法高亮的代码）
                if (codeElement.className && (codeElement.className.includes('language-') || codeElement.className.includes('hljs'))) {
                    return;
                }

                const text = codeElement.textContent || '';

                // 更精确的误识别检测逻辑
                let shouldRemoveCodeTag = false;

                // 情况1: 纯中文文本（可能包含标点符号）
                const chineseAndPunctuation = /^[\u4e00-\u9fff\s，。；：！？（）【】""''、]+$/;
                if (chineseAndPunctuation.test(text)) {
                    shouldRemoveCodeTag = true;
                }

                // 情况2: 包含数学公式的中文描述文本
                // 如："单位是焦耳每立方米（$\text{J/m}^3$）。" 或 "单位是焦耳每立方米（$	ext{J/m}^3$）。"
                const chineseWithMath = /^[\u4e00-\u9fff\s，。；：！？（）【】""''、]*\$[^$]+\$[\u4e00-\u9fff\s，。；：！？（）【】""''、]*$/;
                const chineseWithBrokenMath = /^[\u4e00-\u9fff\s，。；：！？（）【】""''、]*\$\s*ext\{[^}]*\}[^$]*\$[\u4e00-\u9fff\s，。；：！？（）【】""''、]*$/;
                if (chineseWithMath.test(text) || chineseWithBrokenMath.test(text)) {
                    shouldRemoveCodeTag = true;
                }

                // 情况3: 主要是中文的句子，可能包含少量英文或数字
                const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
                const totalChars = text.length;
                const chineseRatio = totalChars > 0 ? chineseChars / totalChars : 0;

                // 检查是否包含明显的代码特征（排除数学公式）
                const textWithoutMath = text.replace(/\$[^$]*\$/g, ''); // 移除数学公式后检查
                const hasCodeFeatures = /[{}();=<>[\]`]/.test(textWithoutMath) ||
                                       /^[a-zA-Z_][a-zA-Z0-9_]*\s*[=()]/.test(textWithoutMath) ||
                                       /^\s*[a-zA-Z_][a-zA-Z0-9_]*\s*$/.test(textWithoutMath);

                // 如果中文比例高且没有代码特征，则移除code标签
                if (chineseRatio > 0.4 && !hasCodeFeatures) {
                    shouldRemoveCodeTag = true;
                }

                // 情况4: 特定的常见误识别模式（包括转义问题导致的变形）
                const commonMisidentifiedPatterns = [
                    /^积分范围覆盖整个电场存在的空间/,
                    /^单位是焦耳每立方米/,
                    /^[\u4e00-\u9fff]+（可能至无穷远）/,
                    /单位是焦耳每立方米.*\$.*ext\{.*\}/,  // 匹配转义问题导致的变形
                    /积分范围覆盖整个电场存在的空间.*（可能至无穷远）/
                ];

                if (commonMisidentifiedPatterns.some(pattern => pattern.test(text))) {
                    shouldRemoveCodeTag = true;
                }

                // 情况5: 检查是否包含被破坏的数学公式（\text 变成了 	ext）
                if (/\$\s*ext\{/.test(text)) {
                    shouldRemoveCodeTag = true;
                }

                if (shouldRemoveCodeTag) {
                    console.log('修复误识别的code标签:', text);
                    // 保持原始HTML结构，只是移除code标签
                    let innerHTML = codeElement.innerHTML;

                    // 修复被破坏的数学公式：将 	ext{ 恢复为 \text{
                    innerHTML = innerHTML.replace(/\$\s*ext\{/g, '$\\text{');

                    const span = document.createElement('span');
                    span.innerHTML = innerHTML;
                    codeElement.parentNode.replaceChild(span, codeElement);
                }
            });

            return tempDiv.innerHTML;
        }

        // HTML后处理，修复特定的误识别情况
        function postProcessHTML(htmlContent) {
            // 直接在HTML字符串中查找和替换特定的误识别模式

            // 修复 "单位是焦耳每立方米（$\text{J/m}^3$）。" 被包裹在code标签中的问题
            htmlContent = htmlContent.replace(/<code>([^<]*单位是焦耳每立方米[^<]*)<\/code>/g, '$1');

            // 修复 "积分范围覆盖整个电场存在的空间（可能至无穷远）。" 被包裹在code标签中的问题
            htmlContent = htmlContent.replace(/<code>([^<]*积分范围覆盖整个电场存在的空间[^<]*)<\/code>/g, '$1');

            // 修复包含破坏的数学公式的文本（各种变形）
            htmlContent = htmlContent.replace(/<code>([^<]*\$[^<]*ext\{[^<]*)<\/code>/g, function(match, content) {
                // 修复破坏的 \text
                const fixedContent = content.replace(/\$\s*ext\{/g, '$\\text{');
                return fixedContent;
            });

            // 修复包含 \\\text 的情况（三个反斜杠）
            htmlContent = htmlContent.replace(/\$([^$]*\\\\\\text\{[^}]*\}[^$]*)\$/g, function(match, formula) {
                // 将 \\\text 修复为 \text
                return match.replace(/\\\\\\text/g, '\\text');
            });

            // 修复任何包含中文且没有明显代码特征的code标签
            htmlContent = htmlContent.replace(/<code>([^<]*[\u4e00-\u9fff][^<]*)<\/code>/g, function(match, content) {
                // 检查是否包含明显的代码特征
                const hasCodeFeatures = /[{}();=<>[\]`]/.test(content) ||
                                       /^[a-zA-Z_][a-zA-Z0-9_]*\s*[=()]/.test(content);

                // 如果没有代码特征，则移除code标签
                if (!hasCodeFeatures) {
                    console.log('后处理修复code标签:', content);
                    // 同时修复可能的数学公式问题
                    let fixedContent = content;
                    fixedContent = fixedContent.replace(/\$\s*ext\{/g, '$\\text{');
                    fixedContent = fixedContent.replace(/\\\\\\text/g, '\\text');
                    return fixedContent;
                }
                return match;
            });

            // 最终清理：处理任何遗留的字面 \n 字符串（不在HTML标签内的）
            htmlContent = htmlContent.replace(/(?<!<[^>]*?)\\n(?![^<]*?>)/g, '<br>');

            return htmlContent;
        }

        // 渲染Markdown回复内容
        function renderMarkdownResponse(content) {
            if (!markdownRenderer) {
                // 如果没有markdown渲染器，也要处理换行符
                let processedContent = preprocessContent(content);
                return escapeHtml(processedContent).replace(/\n/g, '<br>');
            }

            try {
                // 步骤1: 首先处理转义字符，将\n转换为真正的换行符
                let processedContent = preprocessContent(content);

                // 步骤2: 处理数学公式格式问题
                processedContent = fixMathFormulaIssues(processedContent);

                // 步骤3: 预处理数学公式，确保它们有正确的空行
                processedContent = preprocessBlockMath(processedContent);

                // 步骤4: 渲染Markdown
                let rendered = markdownRenderer.render(processedContent);

                // 步骤5: 修复被误识别为代码的普通文本
                rendered = fixMisidentifiedCode(rendered);

                // 步骤6: 额外的HTML后处理，修复特定的误识别情况
                rendered = postProcessHTML(rendered);

                // 步骤7: 最终检查，确保没有遗留的字面 \n 字符串
                rendered = rendered.replace(/\\n/g, '<br>');

                // 渲染完成后，需要处理Mermaid图表
                setTimeout(() => {
                    if (typeof mermaid !== 'undefined') {
                        try {
                            mermaid.init(undefined, document.querySelectorAll('.mermaid:not([data-processed])'));
                        } catch (e) {
                            console.warn('Mermaid渲染警告:', e);
                        }
                    }
                }, 100);

                return rendered;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                let processedContent = preprocessContent(content);
                return escapeHtml(processedContent).replace(/\n/g, '<br>');
            }
        }

        // 切换展开/收起状态
        function toggleExpand(responseId, button) {
            const responseElement = document.getElementById(responseId);
            const isCollapsed = responseElement.classList.contains('collapsed');

            if (isCollapsed) {
                responseElement.classList.remove('collapsed');
                button.textContent = '收起回复';

                // 展开后重新渲染Mermaid图表
                setTimeout(() => {
                    if (typeof mermaid !== 'undefined') {
                        try {
                            const mermaidElements = responseElement.querySelectorAll('.mermaid:not([data-processed])');
                            if (mermaidElements.length > 0) {
                                mermaid.init(undefined, mermaidElements);
                            }
                        } catch (e) {
                            console.warn('Mermaid渲染警告:', e);
                        }
                    }
                }, 100);
            } else {
                responseElement.classList.add('collapsed');
                button.textContent = '展开完整回复';
            }
        }

        // 加载测试数据
        function loadTestData() {
            // 隐藏加载状态
            document.getElementById('loadingIndicator').style.display = 'none';

            // 创建测试数据并暴露到全局作用域
            window.testChats = [
                {
                    user: 'physics_student',
                    user_class: 'student',
                    time: new Date().toISOString(),
                    tokens: 5863,
                    filename: 'electrostatics_lesson.txt',
                    message: '请总结静电势能和电势的概念',
                    response: '这节课的主要内容是讨论静电势能和电势的概念。课程中详细解释了如何计算将两个点电荷从无限远处移动到一定距离时所做的功，并引入了相关的公式。\\n\\n1. **静电势能**：  \\n   静电势能 $ U $ 是指将电荷从无限远处移动到某一点时，外力所做的功。对于两个点电荷 $ Q_1 $ 和 $ Q_2 $，它们之间的静电势能为：\\n\\n$$U = \\\\frac{Q_1 Q_2}{4\\\\pi \\\\varepsilon_0 R}$$\\n\\n其中 $ R $ 是两个电荷之间的距离，$ \\\\varepsilon_0 $ 是真空介电常数。\\n\\n2. **电势**：  \\n   电势 $ V $ 是一个标量，表示单位电荷在电场中某一点的势能。电势与静电势能不同，它不依赖于电荷的大小。\\n\\n3. **保守力的性质**：  \\n   静电力是一种保守力，因此在计算电势能时，路径的选择不影响结果。无论电荷是如何从无限远移动到目标点的，所做的功都是相同的。\\n\\n4. **正负功的含义**：  \\n   - 如果两个电荷同号（均为正或均为负），则需要做正功。\\n   - 如果两个电荷异号（一个正，一个负），则可能需要做负功。\\n\\n5. **多电荷系统的总势能**：  \\n   当系统包含多个电荷时，总势能是所有电荷对之间相互作用的势能之和。\\n\\n本次消耗tokens:5863'
                },
                {
                    user: 'testuser',
                    user_class: 'student',
                    time: new Date(Date.now() - 1800000).toISOString(),
                    tokens: 5167,
                    filename: 'electrostatics_energy.txt',
                    message: '请总结静电势能和电场能量密度的内容',
                    response: '这节课的主要内容是关于**静电势能**和**电场能量密度**的讨论，具体包括以下要点：\\n\\n1. **静电势能的定义**：  \\n   - 组装电荷时需要做功，这部分能量称为**静电势能**（electrostatic potential energy）。  \\n   - 公式表示为：  \\n     $$U = \\\\frac{1}{2} \\\\epsilon_0 E^2 \\\\cdot V$$\\n\\n 其中，$\\\\epsilon_0$ 是真空介电常数，$E$ 是电场强度，$V$ 是体积。\\n\\n2. **电场能量密度**：  \\n   - 通过移动平行板电荷的示例，推导出单位体积内的电场能量密度为：  \\n     $$u_E = \\\\frac{1}{2} \\\\epsilon_0 E^2$$\\n  \\n     单位是焦耳每立方米（$\\\\text{J/m}^3$）。  \\n   - 这一结论不仅适用于平行板，也适用于**任何电荷分布**。\\n\\n3. **能量计算的两种视角**：  \\n   - **电荷视角**：计算组装电荷所需的功。  \\n   - **电场视角**：通过积分电场能量密度得到总能量：  \\n     $$U = \\\\int \\\\frac{1}{2} \\\\epsilon_0 E^2 \\\\, dV$$\\n  \\n     积分范围覆盖整个电场存在的空间（可能至无穷远）。\\n\\n4. **关键示例分析**：  \\n   - 平行板电容器中，移动极板时需克服电场力做功，功的大小与新增的电场体积（$A \\\\cdot x$）和电场强度相关。  \\n   - 电荷层受力分析表明，实际受力为 $F = \\\\frac{1}{2} Q E$，而非 $Q E$（因导体内部电场为零）。\\n\\n5. **符号说明**：  \\n   - 注意区分符号 $V$ 的用途（此处表示体积，而非电势）。  \\n\\n总结：课程通过具体问题展示了静电势能与电场能量的等价性，并强调了电场能量密度的普适性。\\n\\n本次消耗tokens:5167'
                },
                {
                    user: 'testuser',
                    user_class: 'student',
                    time: new Date(Date.now() - 3600000).toISOString(),
                    tokens: 200,
                    filename: 'physics_formulas.txt',
                    message: '什么是麦克斯韦方程组？',
                    response: '麦克斯韦方程组是描述电磁场的基本方程组，包含四个方程：\n\n**高斯定律（电场）：**\n$$\\nabla \\cdot \\vec{E} = \\frac{\\rho}{\\epsilon_0}$$\n\n**高斯定律（磁场）：**\n$$\\nabla \\cdot \\vec{B} = 0$$\n\n**法拉第定律：**\n$$\\nabla \\times \\vec{E} = -\\frac{\\partial \\vec{B}}{\\partial t}$$\n\n**安培-麦克斯韦定律：**\n$$\\nabla \\times \\vec{B} = \\mu_0 \\vec{J} + \\mu_0 \\epsilon_0 \\frac{\\partial \\vec{E}}{\\partial t}$$\n\n这些方程描述了电场 $\\vec{E}$、磁场 $\\vec{B}$、电荷密度 $\\rho$ 和电流密度 $\\vec{J}$ 之间的关系。'
                },
                {
                    user: 'testuser',
                    user_class: 'student',
                    time: new Date(Date.now() - 7200000).toISOString(),
                    tokens: 100,
                    filename: 'inline_math.txt',
                    message: '行内公式测试',
                    response: '这是一个包含行内数学公式的测试：能量公式 $E = mc^2$ 是爱因斯坦最著名的公式。另外，欧拉公式 $e^{i\\pi} + 1 = 0$ 被称为数学中最美的公式。\n\n还有一些常用的数学常数：\n- 圆周率：$\\pi \\approx 3.14159$\n- 自然对数的底：$e \\approx 2.71828$\n- 黄金比例：$\\phi = \\frac{1 + \\sqrt{5}}{2} \\approx 1.618$'
                },
                {
                    user: 'testuser',
                    user_class: 'student',
                    time: new Date(Date.now() - 10800000).toISOString(),
                    tokens: 300,
                    filename: 'maxwell_equations.txt',
                    message: '请显示麦克斯韦方程组的积分形式',
                    response: '麦克斯韦方程组的积分形式如下：\\n\\n\\\\begin{eqnarray}\\n\\\\oint_S \\\\mathbf{E} \\\\cdot d\\\\mathbf{a} &= \\\\frac{Q_{enc}}{\\\\epsilon_0} \\\\\\\\\\n\\\\oint_S \\\\mathbf{B} \\\\cdot d\\\\mathbf{a} &= 0 \\\\\\\\\\n\\\\oint_C \\\\mathbf{E} \\\\cdot d\\\\mathbf{l} &= -\\\\frac{d}{dt}\\\\int_S \\\\mathbf{B} \\\\cdot d\\\\mathbf{a} \\\\\\\\\\n\\\\oint_C \\\\mathbf{B} \\\\cdot d\\\\mathbf{l} &= \\\\mu_0 I_{enc} + \\\\mu_0 \\\\epsilon_0 \\\\frac{d}{dt}\\\\int_S \\\\mathbf{E} \\\\cdot d\\\\mathbf{a}\\n\\\\end{eqnarray}\\n\\n这四个方程分别是：\\n1. 高斯定律（电场）\\n2. 高斯定律（磁场）\\n3. 法拉第电磁感应定律\\n4. 安培-麦克斯韦定律\\n\\n每个方程都有其物理意义，描述了电场和磁场的基本性质和相互关系。'
                }
            ];

            // 显示测试数据
            displayChats(window.testChats);

            // 隐藏分页
            document.getElementById('paginationContainer').style.display = 'none';

            // 显示测试模式提示
            const header = document.querySelector('.header h1');
            if (header) {
                header.textContent = '💬 聊天笔记查看器 (测试模式)';
            }

            // 添加下载修复后内容的按钮
            const headerDiv = document.querySelector('.header');
            if (headerDiv) {
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = '📥 下载修复后的内容';
                downloadBtn.className = 'download-btn';
                downloadBtn.style.marginLeft = '20px';
                downloadBtn.onclick = downloadFixedContent;
                headerDiv.appendChild(downloadBtn);
            }
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 显示分页
        function displayPagination(pagination) {
            const container = document.getElementById('paginationContainer');
            totalPages = pagination.pages;

            if (totalPages <= 1) {
                container.style.display = 'none';
                return;
            }

            container.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '← 上一页';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => loadChats(currentPage - 1);
            container.appendChild(prevBtn);

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = 'page-btn';
                if (i === currentPage) pageBtn.classList.add('active');
                pageBtn.textContent = i;
                pageBtn.onclick = () => loadChats(i);
                container.appendChild(pageBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = '下一页 →';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => loadChats(currentPage + 1);
            container.appendChild(nextBtn);

            // 页面信息
            const pageInfo = document.createElement('span');
            pageInfo.style.margin = '0 15px';
            pageInfo.style.color = '#666';
            pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
            container.appendChild(pageInfo);

            container.style.display = 'flex';
        }

        // 下载AI回复为Markdown文件
        function downloadResponse(responseId, user, time) {
            try {
                // 获取响应元素
                const responseElement = document.getElementById(responseId);
                // 获取父级 chat-item 元素
                const chatItem = responseElement.closest('.chat-item');
                let rawResponse = '';

                // 直接用原始response内容
                if (chatItem) {
                    // 通过DOM还原原始response字段（已在createChatElement作用域）
                    // 由于没有直接存储原始response，需在createChatElement时存储
                    rawResponse = chatItem.dataset.rawResponse || '';
                }
                if (!rawResponse) {
                    rawResponse = '无法获取原始回复内容';
                }

                // 创建文件内容
                const fileContent = `# AI回复\n\n**用户:** ${user}\n**时间:** ${time}\n\n---\n\n${rawResponse}\n`;

                // 创建下载链接
                const blob = new Blob([fileContent], { type: 'text/markdown;charset=utf-8' });
                const url = URL.createObjectURL(blob);

                // 生成文件名
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
                const filename = `AI回复_${user}_${timestamp}.md`;

                // 创建临时下载链接
                const downloadLink = document.createElement('a');
                downloadLink.href = url;
                downloadLink.download = filename;
                downloadLink.style.display = 'none';

                // 添加到DOM，点击下载，然后移除
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);

                // 清理URL对象
                URL.revokeObjectURL(url);

                console.log('下载完成:', filename);
            } catch (error) {
                console.error('下载失败:', error);
                alert('下载失败，请稍后重试');
            }
        }

        // 从DOM重构Markdown内容（作为fallback）
        function reconstructMarkdownFromDOM(element) {
            let markdown = '';

            // 遍历DOM节点，尝试重构Markdown
            function processNode(node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    return node.textContent;
                }

                if (node.nodeType === Node.ELEMENT_NODE) {
                    const tagName = node.tagName.toLowerCase();
                    const content = Array.from(node.childNodes).map(processNode).join('');

                    switch (tagName) {
                        case 'h1':
                            return `# ${content}\n\n`;
                        case 'h2':
                            return `## ${content}\n\n`;
                        case 'h3':
                            return `### ${content}\n\n`;
                        case 'h4':
                            return `#### ${content}\n\n`;
                        case 'h5':
                            return `##### ${content}\n\n`;
                        case 'h6':
                            return `###### ${content}\n\n`;
                        case 'p':
                            return `${content}\n\n`;
                        case 'strong':
                        case 'b':
                            return `**${content}**`;
                        case 'em':
                        case 'i':
                            return `*${content}*`;
                        case 'code':
                            return `\`${content}\``;
                        case 'pre':
                            return `\`\`\`\n${content}\n\`\`\`\n\n`;
                        case 'ul':
                            return `${content}\n`;
                        case 'ol':
                            return `${content}\n`;
                        case 'li':
                            return `- ${content}\n`;
                        case 'blockquote':
                            return `> ${content}\n\n`;
                        case 'br':
                            return '\n';
                        case 'hr':
                            return '---\n\n';
                        default:
                            return content;
                    }
                }

                return '';
            }

            markdown = processNode(element);

            // 清理多余的空行
            markdown = markdown.replace(/\n{3,}/g, '\n\n');
            markdown = markdown.trim();

            return markdown;
        }

        // 直接下载修复后的内容（从控制台输出）
        function downloadFixedContent() {
            // 这是用户提供的修复后的内容
            const fixedContent = `这节课的主要内容是关于**静电势能**和**电场能量密度**的讨论，具体包括以下要点：\\n\\n1. **静电势能的定义**：  \\n   - 组装电荷时需要做功，这部分能量称为**静电势能**（electrostatic potential energy）。  \\n   - 公式表示为：  \\n     \\n\\n$$U = \\\\frac{1}{2} \\\\epsilon_0 E^2 \\\\cdot V$$\\n\\n其中，$\\\\epsilon_0$ 是真空介电常数，$E$ 是电场强度，$V$ 是体积。\\n2. **电场能量密度**：\\n- 通过移动平行板电荷的示例，推导出单位体积内的电场能量密度为：\\n\\n$$u_E = \\\\frac{1}{2} \\\\epsilon_0 E^2$$\\n\\n单位是焦耳每立方米（$\\\\text{J/m}^3$）。\\n- 这一结论不仅适用于平行板，也适用于**任何电荷分布**。\\n3. **能量计算的两种视角**：\\n- **电荷视角**：计算组装电荷所需的功。\\n- **电场视角**：通过积分电场能量密度得到总能量：\\n\\n$$U = \\\\int \\\\frac{1}{2} \\\\epsilon_0 E^2 \\\\, dV$$\\n\\n积分范围覆盖整个电场存在的空间（可能至无穷远）。\\n\\n4. **关键示例分析**：  \\n   - 平行板电容器中，移动极板时需克服电场力做功，功的大小与新增的电场体积（$A \\\\cdot x$）和电场强度相关。  \\n   - 电荷层受力分析表明，实际受力为 $F = \\\\frac{1}{2} Q E$，而非 $Q E$（因导体内部电场为零）。\\n\\n5. **符号说明**：  \\n   - 注意区分符号 $V$ 的用途（此处表示体积，而非电势）。  \\n\\n总结：课程通过具体问题展示了静电势能与电场能量的等价性，并强调了电场能量密度的普适性。\\n\\n本次消耗tokens:5167`;

            // 使用修复后的预处理函数来安全地处理换行符
            const markdownContent = preprocessContent(fixedContent);

            // 创建文件内容
            const fileContent = `# AI回复

**用户:** testuser
**时间:** ${new Date().toLocaleString('zh-CN')}

---

${markdownContent}
`;

            // 创建下载链接
            const blob = new Blob([fileContent], { type: 'text/markdown;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            // 生成文件名
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
            const filename = `修复后的AI回复_${timestamp}.md`;

            // 创建临时下载链接
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = filename;
            downloadLink.style.display = 'none';

            // 添加到DOM，点击下载，然后移除
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            // 清理URL对象
            URL.revokeObjectURL(url);

            console.log('修复后的内容下载完成:', filename);
        }

        // 聊天笔记删除函数（全局）
        async function deleteChat(chatId, btn, chatUser) {
            if (!chatId) return;
            // 非admin只能删自己
            if (!isAdmin && currentUser !== chatUser) {
                alert('只能删除自己的对话！');
                return;
            }
            if (!confirm('确定要删除该对话吗？此操作不可恢复！')) return;
            btn.disabled = true;
            btn.textContent = '删除中...';
            try {
                // 获取当前用户认证参数
                const urlParams = new URLSearchParams(window.location.search);
                const user = urlParams.get('user') || '';
                const key = urlParams.get('key') || '';
                const token = urlParams.get('token') || '';
                const resp = await fetch(`${API_BASE}/delete_chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ chat_id: chatId, user, key, token })
                });
                const data = await resp.json();
                if (data.success) {
                    window.location.replace(window.location.href);
                } else {
                    alert('删除失败：' + (data.error || '未知错误'));
                    window.location.replace(window.location.href);
                }
            } catch (e) {
                alert('请求失败：' + e);
                window.location.replace(window.location.href);
            }
        }

        // 将函数暴露到全局作用域
        window.toggleExpand = toggleExpand;
        window.downloadResponse = downloadResponse;
        window.downloadFixedContent = downloadFixedContent;
        window.deleteChat = deleteChat;

        // 获取修复后的markdown内容（不渲染HTML，仅用于下载）
        function getFixedMarkdown(content) {
            let processedContent = preprocessContent(content);
            processedContent = fixMathFormulaIssues(processedContent);
            processedContent = preprocessBlockMath(processedContent);
            return processedContent;
        }

        // 编辑按钮事件
        window.editChat = function(chatId, btn) {
            // 找到该chat-item
            const chatItem = btn.closest('.chat-item');
            if (!chatItem) return;
            // 获取原始response
            const rawResponse = chatItem.dataset.rawResponse || '';
            // 打开弹窗
            showEditModal(chatId, rawResponse);
        };

        function showEditModal(chatId, rawResponse) {
            const modal = document.getElementById('editModal');
            const textarea = document.getElementById('editResponseTextarea');
            textarea.value = rawResponse.replace(/\\n/g, '\n'); // 显示为多行
            modal.style.display = 'flex';
            // 绑定保存事件
            document.getElementById('editSaveBtn').onclick = function() {
                saveEdit(chatId);
            };
            // 绑定取消事件
            document.getElementById('editCancelBtn').onclick = function() {
                modal.style.display = 'none';
            };
            document.getElementById('editModalClose').onclick = function() {
                modal.style.display = 'none';
            };
        }

        async function saveEdit(chatId) {
            const modal = document.getElementById('editModal');
            const textarea = document.getElementById('editResponseTextarea');
            let newMd = textarea.value;
            // 这里假设后台只需要字符串，不需要特殊json结构
            // 如果需要转义，按原格式转回 \n
            let newResponse = newMd.replace(/\r?\n/g, '\\n');
            // 获取认证参数
            const urlParams = new URLSearchParams(window.location.search);
            const user = urlParams.get('user') || '';
            const key = urlParams.get('key') || '';
            const token = urlParams.get('token') || '';
            // 调用编辑接口
            try {
                const resp = await fetch(`${API_BASE}/edit_chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ chat_id: chatId, user, key, token, response: newResponse })
                });
                const data = await resp.json();
                if (data.success) {
                    alert('保存成功！');
                    modal.style.display = 'none';
                    window.location.replace(
                      window.location.pathname +
                      window.location.search.replace(/([?&])_=[^&]*/g, '').replace(/^&/, '?') +
                      (window.location.search ? '&' : '?') + '_=' + Date.now()
                    );
                    window.location.reload();
                } else {
                    alert('保存失败：' + (data.error || '未知错误'));
                }
            } catch (e) {
                alert('请求失败：' + e);
            }
        }

        window.editChat = editChat;
    </script>

    <!-- 编辑弹窗 -->
    <div id="editModal" style="display:none;position:fixed;z-index:9999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);align-items:center;justify-content:center;">
      <div style="background:#fff;border-radius:10px;max-width:700px;width:90vw;padding:30px;box-shadow:0 8px 32px rgba(0,0,0,0.2);position:relative;">
        <h2 style="margin-bottom:15px;">编辑AI回复</h2>
        <textarea id="editResponseTextarea" style="width:100%;height:300px;font-size:16px;padding:10px;border-radius:8px;border:1px solid #ccc;resize:vertical;"></textarea>
        <div style="margin-top:20px;text-align:right;">
          <button id="editCancelBtn" class="expand-btn" style="margin-right:10px;">取消</button>
          <button id="editSaveBtn" class="download-btn">保存</button>
        </div>
        <span id="editModalClose" style="position:absolute;top:10px;right:18px;font-size:22px;cursor:pointer;">×</span>
      </div>
    </div>
</body>
</html>
