import shutil
from flask import Flask, send_file, request, Response, jsonify, redirect, send_from_directory, abort,render_template_string, make_response, session, url_for, render_template
import os
import re
import hashlib
import threading
import glob
import mimetypes
from functools import wraps
from waitress import serve
import requests
from getDocId import sync_user_files_with_doc_ids
import uuid
import json
import urllib.parse
import traceback
from werkzeug.middleware.proxy_fix import ProxyFix
from pathlib import Path
from werkzeug.utils import secure_filename
import logging
from web.blog_page import reset_user_counts
from flask_cors import CORS
from werkzeug.utils import secure_filename
from urllib.parse import unquote
import logging
import pytz
import subprocess
from bs4 import BeautifulSoup
from collections import defaultdict
import yaml
import bcrypt
import time
import secrets
import random
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
import yaml
from getAnswer import  SmartChatbot, get_matching_ids
from getDocId import upload_or_get_doc_id,uploadvideo_or_get_doc_id
from datetime import datetime, timedelta,timezone
from chatWithFile import process_query
import time
import psutil
import gc
from logger_config import log_with_request_id  # 从新模块导入
from logger_config import video_server_logger as logger, CustomTransLogger
import cv2
from PIL import Image
from getSubtitles_server import is_user_vip
from queue import Queue, Empty
from typing import Dict, Any
from question_generator import QuestionGenerator
from class_user_data.record_class_user_data import record_class_user_data, record_video_viewing
from class_user_data.analyze_questions import QuestionAnalyzer
####################################################################################


####################################################################################

#################################################################################################




app = Flask(__name__)

app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1)
app.secret_key = 'your-secret-key-here'  # 设置session密钥

# 配置session持久化
from datetime import timedelta
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)  # session持续24小时
app.config['SESSION_PERMANENT'] = True  # 启用持久session

api_key_model= "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"
# app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 设置最大上传大小为 1GB
CORS(app)  # 这将允许所有域的跨域请求


######################################################################################
# 导入主应用蓝图
######################################################################################
#新增的路由
######################################################################################
current_dir = os.path.dirname(os.path.abspath(__file__))
# 导入 app.py 中定义的蓝图和初始化函数
from app import main_bp, init_app

# 主应用配置文件路径
CONFIG_FILE = 'config.yaml'
USER_CONFIG_FILE = 'user_configs.json'
USER_FILES_JSON = 'user/user_files.json'
DOC_IDS_JSON = 'ragdoc/doc_ids.json'
USER_RAG_COUNTS_FILE = 'user_rag_counts.json'

# API配置
BASE_URL = "http://localhost:8080/v1/api"
API_KEY_MODEL = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"
API_KEY_DEEPSEEK = "***********************************"
API_KEY_SILICONFLOW = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb"
API_KEY_QWEN = "sk-61010d52e30c4fb096d240ad7fae39df"

# 上传限制
MAX_RAG_UPLOADS = 2
MAX_VIDEO_UPLOADS = 1
VIP_EXTRA_RAG_UPLOADS = 10
VIP_EXTRA_VIDEO_UPLOADS = 10

# 允许的文件类型
ALLOWED_EXTENSIONS = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml']

# send_verification_email函数已移至app.py，避免重复定义




# 主应用辅助函数
def load_main_config():
    """加载主应用配置文件"""
    if not os.path.exists(CONFIG_FILE):
        return get_default_main_config()
    with open(CONFIG_FILE, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def get_default_main_config():
    """获取默认配置"""
    return {
        'credentials': {
            'usernames': {}
        }
    }

def save_main_config(config):
    """保存主应用配置文件"""
    with open(CONFIG_FILE, 'w', encoding='utf-8') as file:
        yaml.dump(config, file, default_flow_style=False, allow_unicode=True)

def hash_password(password):
    """哈希密码"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(stored_password, provided_password):
    """验证密码"""
    if stored_password.startswith('$2b$'):
        return bcrypt.checkpw(provided_password.encode('utf-8'), stored_password.encode('utf-8'))
    else:
        return stored_password == provided_password

def get_user_upload_limits(username):
    """获取用户的上传限制次数，考虑 VIP 状态"""
    limits = {
        'rag': MAX_RAG_UPLOADS,
        'video': MAX_VIDEO_UPLOADS
    }

    try:
        config = load_main_config()
        if username == "admin":
            return {
                'rag': 999999,  # 使用一个很大的数字而不是无穷大
                'video': 999999
            }

        if username in config.get('credentials', {}).get('usernames', {}):
            user_info = config['credentials']['usernames'][username]
            if 'vip_expiry' in user_info:
                expiry_timestamp = user_info['vip_expiry']
                if expiry_timestamp > time.time():
                    limits['rag'] += VIP_EXTRA_RAG_UPLOADS
                    limits['video'] += VIP_EXTRA_VIDEO_UPLOADS
    except Exception as e:
        logging.error(f"Error checking VIP status for upload limits: {str(e)}")

    return limits

def manage_user_rag_count(username, increment=False):
    """管理用户RAG上传次数"""
    if not os.path.exists(USER_RAG_COUNTS_FILE):
        with open(USER_RAG_COUNTS_FILE, 'w') as f:
            json.dump({}, f, ensure_ascii=False)

    with open(USER_RAG_COUNTS_FILE, 'r') as f:
        counts = json.load(f)

    if username not in counts:
        counts[username] = 0

    if increment:
        counts[username] += 1
        with open(USER_RAG_COUNTS_FILE, 'w') as f:
            json.dump(counts, f, ensure_ascii=False)

    return counts[username]

def update_user_files(username, filename):
    """更新用户文件记录"""
    os.makedirs(os.path.dirname(USER_FILES_JSON), exist_ok=True)
    user_files = {}

    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                content = f.read()
                if content:
                    user_files = json.loads(content)
        except (json.JSONDecodeError, Exception) as e:
            print(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")

    if username not in user_files:
        user_files[username] = []

    if filename not in user_files[username]:
        user_files[username].append(filename)

    try:
        with open(USER_FILES_JSON, 'w', encoding='utf-8') as f:
            json.dump(user_files, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"写入 {USER_FILES_JSON} 时发生错误: {str(e)}")

def get_user_token(username, config):
    """获取用户令牌"""
    user_info = config.get('credentials', {}).get('usernames', {}).get(username, {})

    if 'token' not in user_info or 'expiry' not in user_info:
        return update_user_token(username, config)

    current_time = time.time()
    if current_time > user_info['expiry']:
        return update_user_token(username, config)

    return user_info['token']

def update_user_token(username, config):
    """更新用户令牌"""
    token = secrets.token_urlsafe(32)
    expiry = time.time() + 720 * 3600  # 720小时（30天）后过期

    if 'credentials' not in config:
        config['credentials'] = {'usernames': {}}
    if 'usernames' not in config['credentials']:
        config['credentials']['usernames'] = {}
    if username not in config['credentials']['usernames']:
        config['credentials']['usernames'][username] = {}

    config['credentials']['usernames'][username]['token'] = token
    config['credentials']['usernames'][username]['expiry'] = expiry

    save_main_config(config)
    return token
# VIDEO_DIR = 'D:/github/ragproj/prj_web/video'  # 更改为你的视频目录路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
VIDEO_DIR = os.path.join(SCRIPT_DIR, "video")



# # 定义多个视频目录
# VIDEO_DIRS = {
#     'default': VIDEO_DIR,  # 保留原有的视频目录
#     'mit_physics': r"D:\github\ragproj\coursevideo\MIT Physics II Electricity and Magnetism"
# }


main_dir = r"D:\github\ragproj"
COURSE_VIDEO_BASE = os.path.join(main_dir, 'coursevideo')
# # 确保所有视频目录存在
# for dir_name, dir_path in VIDEO_DIRS.items():
#     if not os.path.exists(dir_path):
#         os.makedirs(dir_path, exist_ok=True)
#         logger.warning(f"Created video directory for {dir_name}: {dir_path}")
#     else:
#         logger.info(f"Using video directory for {dir_name}: {dir_path}")






# 定义博客文件的存储目录
BLOG_DIR = os.path.join(SCRIPT_DIR, "blogs")
# 确保博客目录存在
os.makedirs(BLOG_DIR, exist_ok=True)
# 用户博客记录文件路径
USER_BLOGS_FILE = os.path.join(SCRIPT_DIR, "user_blogs.json")
COMMENTS_FILE = os.path.join(SCRIPT_DIR, "comments.json")
# 评论文件夹路径
COMMENTS_DIR = os.path.join(SCRIPT_DIR, "comments")
# 确保评论目录存在
os.makedirs(COMMENTS_DIR, exist_ok=True)
# 文件路径常量blog访问量
VIEWS_FILE = os.path.join(SCRIPT_DIR, "blog_views.json")
# 确保这个路径存在并且可写
UPLOAD_FOLDER = os.path.join(SCRIPT_DIR, "uploads_Img")
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)


#rag文件记录文档
USER_FILES_JSON = os.path.join(SCRIPT_DIR, "user", "user_files.json")
DOC_IDS_JSON = os.path.join(SCRIPT_DIR, "ragdoc", "doc_ids.json")


ATTACHMENT_FOLDER = os.path.join(SCRIPT_DIR, "attachments")
ALLOWED_ATTACHMENT_EXTENSIONS = {'zip', 'rar', '7z'}
if not os.path.exists(ATTACHMENT_FOLDER):
    os.makedirs(ATTACHMENT_FOLDER) 
###########################################################    
# 添加新的常量用户blog每日上传限制次数
DAILY_UPLOAD_LIMITS = {
    'image': 30,
    'video': 3,
    'attachment': 5
}

# 新增: 用户每日blog和视频对话次数限制
DAILY_CHAT_LIMIT = 50
# 新增: 用户每日文件对话次数限制
DAILY_FILECHAT_LIMIT=50
#vip增加的限制
vip_add_blog_limit=20
vip_add_chat_limit=50
vip_add_filechat_limit=50



###############################################################
USER_UPLOAD_COUNTS_FILE = os.path.join(SCRIPT_DIR, "user_blog_upload_counts.json")
if not os.path.exists(USER_UPLOAD_COUNTS_FILE):
    with open(USER_UPLOAD_COUNTS_FILE, 'w', encoding='utf-8') as f:
        json.dump({}, f)    



#blog视频文件夹
BLOG_VIDEO_DIR = os.path.join(SCRIPT_DIR, 'blog_video')
if not os.path.exists(BLOG_VIDEO_DIR):
    os.makedirs(BLOG_VIDEO_DIR)

########################
chat_id="3b58bf3e8e2611ef98110242ac120006" #blog_chat
chat_id_test="88a31ba86e1011efa2e10242ac120002" #test_chat

############################
#删除知识库
base_url = "http://localhost:8080/v1/api"  # 确保这是正确的基础URL
api_key = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"  # 替换为您的test实际API密钥
kb_id = "test"  # 替换为您的实际知识库ID
api_key_blog = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"  # 替换为您的blog实际API密钥
kb_id_blog = "blog"  # 替换为您的blog实际知识库ID   




# 在应用启动时初始化文件
if not os.path.exists('user_submit_counts.json'):
    with open('user_submit_counts.json', 'w', encoding='utf-8') as f:
        json.dump({}, f)

if not os.path.exists('user_upload_counts.json'):
    with open('user_upload_counts.json', 'w', encoding='utf-8') as f:
        json.dump({}, f)

if not os.path.exists('user_rag_counts.json'):
    with open('user_rag_counts.json', 'w', encoding='utf-8') as f:
        json.dump({}, f)

if not os.path.exists('total_user_activity_counts.json'):
    with open('total_user_activity_counts.json', 'w', encoding='utf-8') as f:
        json.dump({}, f)

if not os.path.exists('web/last_reset.json'):
    with open('web/last_reset.json', 'w', encoding='utf-8') as f:
        json.dump({"last_reset": datetime.now().isoformat()}, f)

if not os.path.exists(USER_UPLOAD_COUNTS_FILE):
    with open(USER_UPLOAD_COUNTS_FILE, 'w', encoding='utf-8') as f:
        json.dump({}, f)




######################################################################################

# 404错误处理 - 过滤恶意扫描
@app.errorhandler(404)
def handle_404(e):
    """处理404错误，过滤常见的恶意扫描请求"""
    path = request.path

    # 定义常见的恶意扫描路径
    malicious_patterns = [
        '.env', 'phpinfo', 'app_dev.php', 'cgi-bin',
        'config.yaml', 'wp-admin', 'admin.php', 'phpmyadmin',
        'staging', 'backend', 'laravel', 'demo', 'dev',
        'info/', 'test.php', 'index.php', 'robots.txt'
    ]

    # 检查是否为恶意扫描
    is_malicious = any(pattern in path.lower() for pattern in malicious_patterns)

    if is_malicious:
        # 对于恶意扫描，只记录简单的警告，不记录详细堆栈
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        logger.warning(f"Blocked malicious scan attempt from {client_ip}: {request.method} {path}")
    else:
        # 对于正常的404错误，记录详细信息
        logger.error(f"404 Not Found: {request.method} {path}")

    return jsonify({
        'success': False,
        'error': 'Not Found',
        'message': 'The requested URL was not found on the server.'
    }), 404

# 全局错误处理
@app.errorhandler(Exception)
def handle_exception(e):
    # 如果是404错误，让专门的处理器处理
    if hasattr(e, 'code') and e.code == 404:
        return handle_404(e)

    logger.error(f'Unhandled exception: {str(e)}')
    logger.error(traceback.format_exc())

    return jsonify({
        'success': False,
        'error': str(e),
        'message': 'An internal server error occurred'
    }), 500

# 健康检查端点
@app.route('/health')
def health_check():
    """增强的健康检查端点"""
    start_time = time.time()
    request_id = str(uuid.uuid4())[:8]
    
    try:
        process = psutil.Process()
        health_info = {
            "status": "healthy",
            "message": "Server is running",
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu_percent": process.cpu_percent(interval=0.1),
                "memory_percent": process.memory_percent(),
                "connections": len(process.connections()),
                "threads": process.num_threads()
            }
        }
        
        # 计算响应时间
        response_time = (time.time() - start_time) * 1000
        health_info["response_time_ms"] = round(response_time, 2)
        
        # # 使用自定义logger记录信息
        # api_logger.info(
        #     f"Health check completed in {health_info['response_time_ms']}ms",
        #     extra={'request_id': request_id}
        # )
        
        return jsonify(health_info), 200
        
    except Exception as e:
        # api_logger.error(
        #     f"Health check failed: {str(e)}",
        #     extra={'request_id': request_id}
        # )
        return jsonify({
            "status": "unhealthy",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

# def setup_logger():
#     """配置日志处理器"""
#     log_dir = os.path.join(SCRIPT_DIR, "logs")
#     os.makedirs(log_dir, exist_ok=True)
    
#     log_file = os.path.join(log_dir, f'api_{datetime.now().strftime("%Y%m%d")}.log')
    
#     # 使用自定义的处理器
#     file_handler = LimitedTimedRotatingFileHandler(
#         filename=log_file,
#         max_lines=5000,  # 限制最大行数
#         when='midnight',  # 每天午夜轮转
#         interval=1,
#         backupCount=7,   # 保留7天的备份
#         encoding='utf-8'
#     )
    
#     # 设置日志格式
#     formatter = logging.Formatter(
#         '%(asctime)s - %(levelname)s - [%(request_id)s] - %(message)s'
#     )
#     file_handler.setFormatter(formatter)
    
#     # 配置logger
#     logger = logging.getLogger('api_logger')
#     logger.setLevel(logging.INFO)
#     logger.addHandler(file_handler)
    
#     return logger

# api_logger = setup_logger()



# 添加连接监控端点
@app.route('/metrics')
def metrics():
    process = psutil.Process()
    
    return jsonify({
        'cpu_percent': process.cpu_percent(),
        'memory_percent': process.memory_percent(),
        'connections': len(process.connections()),
        'threads': process.num_threads(),
        'open_files': len(process.open_files())
    })











######################################################################################
#loadconfig
######################################################################################
user_key_config = None
user_key_config_file = os.path.join(SCRIPT_DIR, 'config.yaml')

def load_config():
    """加载配置文件"""
    try:
        with open(user_key_config_file, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    except Exception as e:
        logger.error(f"Error loading config: {str(e)}")
        return None

# 初始化配置
user_key_config = load_config()

def update_config_if_needed():
    """更新配置文件"""
    global user_key_config
    try:
        new_config = load_config()
        if new_config is not None:
            user_key_config = new_config
            logger.debug("Config updated successfully")
            return True
        return False
    except Exception as e:
        logger.error(f"Error updating config: {str(e)}")
        return False

def require_auth(f):
    """装饰器：要求用户认证和token验证"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 更新配置
        update_config_if_needed()

        # 获取参数
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')

        # 参数验证
        if not user or not key:
            logger.warning("Missing user or key")
            return jsonify({
                'success': False,
                'message': 'Missing user or key'
            }), 401

        # 用户认证
        if not authenticate_user(user, key):
            logger.warning(f"Authentication failed for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid user or key'
            }), 401

        # Token 验证
        if not verify_access_token(user, token, user_key_config):
            logger.warning(f"Token verification failed for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid token'
            }), 401

        return f(*args, **kwargs)

    return decorated_function

############################################################################

# 创建访问计数器文件路径
TOOL_VISITS_FILE = os.path.join(SCRIPT_DIR, 'tool_visits.json')

def load_tool_visits():
    """加载工具访问次数"""
    try:
        if os.path.exists(TOOL_VISITS_FILE):
            with open(TOOL_VISITS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return defaultdict(int)
    except Exception as e:
        logger.error(f"Error loading tool visits: {str(e)}")
        return {
                'ai_demo': 0,
                'electric_demo': 0,
                'latex_editor': 0,
                'markdown_renderer': 0,
                'circuit': 0,
                'paint_board': 0,
                'florence': 0,
                'onnxdemo': 0
            }




def save_tool_visits(visits):
    """保存工具访问次数"""
    try:
        with open(TOOL_VISITS_FILE, 'w', encoding='utf-8') as f:
            json.dump(visits, f)
    except Exception as e:
        logger.error(f"Error saving tool visits: {str(e)}")

def increment_tool_visits(tool_name):
    """增加工具访问次数"""
    visits = load_tool_visits()
    visits[tool_name] = visits.get(tool_name, 0) + 1
    save_tool_visits(visits)
    return visits[tool_name]

# 添加获取访问次数的API端点
@app.route('/api/tool-visits/')
def get_tool_visits():
    try:
        visits = load_tool_visits()
        # 确保所有工具都有初始值
        default_tools = {
            'ai_demo': 0,
            'aiinpaint': 0,
            'paint_board': 0,
            'florence': 0,
            'electric': 0,
            'latex_editor': 0,
            'markdown_renderer': 0,
            'circuit': 0,
            'onnxdemo': 0
        }
        # 更新默认值与实际访问数据
        default_tools.update(visits)

        print(f"Returning tool visits: {default_tools}")  # 调试日志
        return jsonify(default_tools)

    except Exception as e:
        print(f"Error getting tool visits: {str(e)}")
        return jsonify({
            'error': str(e)
        }), 500

# 添加dashboard兼容的API端点
@app.route('/api/get-tool-visits')
def get_tool_visits_dashboard():
    try:
        visits = load_tool_visits()
        # 确保所有工具都有初始值，包括新添加的工具
        default_tools = {
            'ai_demo': 0,
            'electric_demo': 0,  # 对应electric.html
            'latex_editor': 0,
            'markdown_renderer': 0,
            'circuit': 0,
            'paint_board': 0,
            'florence': 0,
            'onnxdemo': 0,
            'gen_questions': 0
        }
        # 更新默认值与实际访问数据
        default_tools.update(visits)

        # 处理字段名映射（electric -> electric_demo）
        if 'electric' in visits:
            default_tools['electric_demo'] = visits['electric']

        print(f"Returning dashboard tool visits: {default_tools}")  # 调试日志
        return jsonify({
            'success': True,
            'tool_visits': default_tools
        })

    except Exception as e:
        print(f"Error getting dashboard tool visits: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取工具访问次数失败: {str(e)}'
        }), 500
    






######################################################################################
#onnxdemo   
######################################################################################
@app.route('/api/onnxdemo/')
def onnxdemo():
    try:
        logger.info("Starting onnxdemo route handler")
        
        # 修正工具访问计数器
        increment_tool_visits('onnxdemo')
        
        # 1. 获取并验证参数
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')
        
        logger.info(f"Request parameters - User: {user}")
        
        # 2. 参数验证
        if not user or not key:
            logger.warning("Missing user or key")
            return jsonify({
                'success': False,
                'message': 'Missing user or key'
            }), 401

        # 3. 用户认证
        try:
            if not authenticate_user(user, key):
                logger.warning(f"Authentication failed for user: {user}")
                return jsonify({
                    'success': False,
                    'message': 'Invalid user or key'
                }), 401
        except Exception as auth_error:
            logger.error(f"Authentication error: {str(auth_error)}")
            return jsonify({
                'success': False,
                'message': f'Authentication error: {str(auth_error)}'
            }), 500

        # 4. Token 验证
        update_config_if_needed()
        try:
            if not verify_access_token(user, token, user_key_config):
                logger.warning(f"Token verification failed for user: {user}")
                return jsonify({
                    'success': False,
                    'message': 'Invalid token'
                }), 401
        except Exception as token_error:
            logger.error(f"Token verification error: {str(token_error)}")
            return jsonify({
                'success': False,
                'message': f'Token verification error: {str(token_error)}'
            }), 500

        # 5. 读取并返回 index.html
        index_path = os.path.join(SCRIPT_DIR, 'onnxdemo', 'index.html')
        logger.info(f"Looking for index.html at: {index_path}")
        
        # 检查目录是否存在
        onnxdemo_dir = os.path.join(SCRIPT_DIR, 'onnxdemo')
        if not os.path.exists(onnxdemo_dir):
            logger.error(f"onnxdemo directory not found at: {onnxdemo_dir}")
            return jsonify({
                'success': False,
                'message': 'onnxdemo directory not found',
                'path': onnxdemo_dir
            }), 404
            
        # 检查文件是否存在
        if not os.path.exists(index_path):
            logger.error(f"index.html not found at path: {index_path}")
            return jsonify({
                'success': False,
                'message': 'index.html file not found',
                'path': index_path
            }), 404
            
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.info(f"Successfully read index.html, content length: {len(content)}")

            # 添加调试脚本
            debug_script = """
            <script>
                // 页面加载完成时的处理
                window.addEventListener('load', function() {
                    console.log('Page loaded');
                    console.log('Root element:', document.getElementById('app'));
                });

                // 错误处理
                window.onerror = function(msg, url, line, col, error) {
                    console.error('Error:', {
                        message: msg,
                        url: url,
                        line: line,
                        column: col,
                        error: error
                    });
                    // 显示错误信息在页面上
                    const errorDiv = document.createElement('div');
                    errorDiv.style.color = 'red';
                    errorDiv.style.padding = '20px';
                    errorDiv.innerHTML = `Error: ${msg}<br>Line: ${line}<br>Column: ${col}`;
                    document.body.appendChild(errorDiv);
                };

                // 资源加载错误处理
                window.addEventListener('error', function(e) {
                    if (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') {
                        console.error('Resource loading error:', {
                            element: e.target.tagName,
                            src: e.target.src || e.target.href
                        });
                    }
                }, true);
            </script>
            """
            
            content = content.replace('</head>', f'{debug_script}</head>')

            # 设置响应头
            headers = {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'X-Content-Type-Options': 'nosniff'
            }

            return content, 200, headers

        except Exception as e:
            logger.error(f"Error reading index.html: {str(e)}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return jsonify({
                'success': False,
                'message': f'Error reading frontend files: {str(e)}',
                'traceback': traceback.format_exc()
            }), 500

    except Exception as e:
        logger.error(f"Error in onnxdemo: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': str(e),
            'traceback': traceback.format_exc()
        }), 500

# 将资源路由移到外面
@app.route('/api/onnxdemo/onnxruntime-web-demo/<path:filename>')
def serve_onnxdemopng_assets(filename):
    try:
        logger.info(f"Requesting asset: {filename}")
        assets_dir = os.path.join(SCRIPT_DIR, 'onnxdemo')
        
        if not os.path.exists(assets_dir):
            logger.error(f"Assets directory not found: {assets_dir}")
            return jsonify({'error': 'Assets directory not found'}), 404
        
        file_path = os.path.join(assets_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Asset file not found: {file_path}")
            return jsonify({'error': 'Asset file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.png': 'image/png',
            '.onnx': 'application/octet-stream',
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(assets_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except Exception as e:
        logger.error(f"Error serving asset {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500




@app.route('/api/onnxdemo/test-css/<path:filename>')
def test_css_file(filename):
    try:
        logger.info(f"=== Testing CSS file access ===")
        logger.info(f"Filename: {filename}")
        logger.info(f"SCRIPT_DIR: {SCRIPT_DIR}")
        
        css_dir = os.path.join(SCRIPT_DIR, 'onnxdemo', 'css')
        file_path = os.path.join(css_dir, filename)
        
        logger.info(f"CSS directory: {css_dir}")
        logger.info(f"Full file path: {file_path}")
        
        # 检查目录是否存在
        if not os.path.exists(css_dir):
            logger.error(f"CSS directory does not exist: {css_dir}")
            # 检查父目录
            parent_dir = os.path.dirname(css_dir)
            parent_contents = os.listdir(parent_dir) if os.path.exists(parent_dir) else []
            return jsonify({
                'error': 'CSS directory not found',
                'css_dir': css_dir,
                'parent_dir': parent_dir,
                'parent_contents': parent_contents
            })
        
        # 目录存在，列出内容
        dir_contents = os.listdir(css_dir)
        logger.info(f"Directory contents: {dir_contents}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"CSS file not found: {file_path}")
            return jsonify({
                'error': 'File not found',
                'filename': filename,
                'file_path': file_path,
                'directory_contents': dir_contents
            })
        
        # 获取文件信息
        file_stat = os.stat(file_path)
        file_info = {
            'exists': True,
            'path': file_path,
            'size': file_stat.st_size,
            'permissions': oct(file_stat.st_mode)[-3:],
            'directory_exists': True,
            'directory_path': css_dir,
            'directory_contents': dir_contents,
            'is_file': os.path.isfile(file_path),
            'is_readable': os.access(file_path, os.R_OK)
        }
        
        # 尝试读取文件前100个字节
        try:
            with open(file_path, 'rb') as f:
                preview = f.read(100)
                file_info['content_preview'] = str(preview[:50])
                file_info['can_read'] = True
        except Exception as read_error:
            file_info['can_read'] = False
            file_info['read_error'] = str(read_error)
        
        logger.info(f"File info: {file_info}")
        return jsonify(file_info)
        
    except Exception as e:
        logger.error(f"=== Error in test_css_file ===")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error message: {str(e)}")
        logger.error(f"Traceback:\n{traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'error_type': type(e).__name__,
            'traceback': traceback.format_exc(),
            'script_dir': SCRIPT_DIR,
            'attempted_path': os.path.join(SCRIPT_DIR, 'onnxdemo', 'css', filename)
        }), 500

@app.route('/api/onnxdemo/onnxruntime-web-demo/css/<path:filename>')
def serve_onnxdemocss_assets(filename):
    try:
        logger.info(f"=== CSS Asset Request Debug ===")
        logger.info(f"Requested filename: {filename}")
        logger.info(f"SCRIPT_DIR: {SCRIPT_DIR}")
        
        # 构建完整路径
        assets_dir = os.path.join(SCRIPT_DIR, 'onnxdemo', 'css')
        file_path = os.path.join(assets_dir, filename)
        
        logger.info(f"Full assets_dir path: {assets_dir}")
        logger.info(f"Full file path: {file_path}")
        logger.info(f"Directory exists: {os.path.exists(assets_dir)}")
        logger.info(f"File exists: {os.path.exists(file_path)}")
        
        # 如果目录存在，列出其内容
        if os.path.exists(assets_dir):
            logger.info(f"Directory contents: {os.listdir(assets_dir)}")
        else:
            logger.error("CSS directory not found!")
            # 检查父目录
            parent_dir = os.path.dirname(assets_dir)
            if os.path.exists(parent_dir):
                logger.info(f"Parent directory contents: {os.listdir(parent_dir)}")
            return jsonify({
                'error': 'CSS directory not found',
                'path': assets_dir,
                'parent_contents': os.listdir(parent_dir) if os.path.exists(parent_dir) else []
            }), 404

        # 如果文件不存在
        if not os.path.exists(file_path):
            logger.error(f"CSS file not found: {file_path}")
            return jsonify({
                'error': 'CSS file not found',
                'requested_file': filename,
                'full_path': file_path,
                'directory_contents': os.listdir(assets_dir) if os.path.exists(assets_dir) else []
            }), 404

        try:
            # 尝试读取文件内容（用于调试）
            with open(file_path, 'r', encoding='utf-8') as f:
                content_preview = f.read(100)  # 读取前100个字符
                logger.info(f"File content preview: {content_preview}")
        except Exception as read_error:
            logger.error(f"Error reading file: {str(read_error)}")

        # 设置响应
        try:
            response = send_from_directory(assets_dir, filename)
            response.headers['Content-Type'] = 'text/css'
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            logger.info("Successfully created response")
            return response
        except Exception as send_error:
            logger.error(f"Error sending file: {str(send_error)}")
            raise

    except Exception as e:
        logger.error(f"=== Error in CSS route ===")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error message: {str(e)}")
        logger.error(f"Full traceback:\n{traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'error_type': type(e).__name__,
            'traceback': traceback.format_exc(),
            'filename': filename,
            'attempted_path': os.path.join(SCRIPT_DIR, 'onnxdemo', 'css', filename)
        }), 500




@app.route('/api/onnxdemo/onnxruntime-web-demo/js/<path:filename>')
def serve_onnxdemojs_assets(filename):
    try:
        logger.info(f"Requesting asset: {filename}")
        assets_dir = os.path.join(SCRIPT_DIR, 'onnxdemo', 'js')
        if not os.path.exists(assets_dir):
            logger.error(f"Assets directory not found: {assets_dir}")
            return jsonify({'error': 'Assets directory not found'}), 404
        
        file_path = os.path.join(assets_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Asset file not found: {file_path}")
            return jsonify({'error': 'Asset file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.js': 'application/javascript',
            '.css': 'text/css',
            '.wasm': 'application/wasm',
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(assets_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except Exception as e:
        logger.error(f"Error serving asset {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500


@app.route('/api/onnxdemo/onnxruntime-web-demo/images/<path:filename>')
def serve_onnxdemoimages_assets(filename):
    try:
        logger.info(f"Requesting asset: {filename}")
        assets_dir = os.path.join(SCRIPT_DIR, 'onnxdemo', 'img')
        
        if not os.path.exists(assets_dir):
            logger.error(f"Assets directory not found: {assets_dir}")
            return jsonify({'error': 'Assets directory not found'}), 404
        
        file_path = os.path.join(assets_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Asset file not found: {file_path}")
            return jsonify({'error': 'Asset file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(assets_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except Exception as e:
        logger.error(f"Error serving asset {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500



    
@app.route('/api/onnxdemo/debug')
def debug_onnxdemo():
    try:
        debug_info = {
            'SCRIPT_DIR': SCRIPT_DIR,
            'onnxdemo_dir': os.path.join(SCRIPT_DIR, 'onnxdemo'),
            'index_path': os.path.join(SCRIPT_DIR, 'onnxdemo', 'index.html'),
            'directory_structure': {},
            'file_permissions': {},
            'exists': {}
        }
        
        # 检查目录是否存在
        onnxdemo_dir = os.path.join(SCRIPT_DIR, 'onnxdemo')
        debug_info['exists']['onnxdemo_dir'] = os.path.exists(onnxdemo_dir)
        
        if os.path.exists(onnxdemo_dir):
            # 列出目录内容
            debug_info['directory_structure'] = {
                'onnxdemo': os.listdir(onnxdemo_dir)
            }
            
            # 检查 index.html
            index_path = os.path.join(onnxdemo_dir, 'index.html')
            debug_info['exists']['index_html'] = os.path.exists(index_path)
            
            if os.path.exists(index_path):
                debug_info['file_permissions']['index_html'] = {
                    'mode': oct(os.stat(index_path).st_mode)[-3:],
                    'size': os.path.getsize(index_path)
                }
        
        return jsonify(debug_info)
        
    except Exception as e:
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

















######################################################################################
#NewFun1_florence
######################################################################################


@app.route('/api/florence/')
def florence():
    update_config_if_needed()
    increment_tool_visits('florence')
    try:
        # 1. 获取并验证参数
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')
        
        logger.info(f"Request parameters - User: {user}")
        
        # 2. 参数验证
        if not user or not key:
            logger.warning("Missing user or key")
            return jsonify({
                'success': False,
                'message': 'Missing user or key'
            }), 401

        # 3. 用户认证
        if not authenticate_user(user, key):
            logger.warning(f"Authentication failed for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid user or key'
            }), 401

        # 4. Token 验证
        if not verify_access_token(user, token, user_key_config):
            logger.warning(f"Token verification failed for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid token'
            }), 401

        # 5. 读取并返回 index.html
        index_path = os.path.join(SCRIPT_DIR, 'florence', 'index.html')
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 添加调试脚本
            debug_script = """
            <script>
                // 页面加载完成时的处理
                window.addEventListener('load', function() {
                    console.log('Page loaded');
                    console.log('Root element:', document.getElementById('root'));
                });

                // 错误处理
                window.onerror = function(msg, url, line, col, error) {
                    console.error('Error:', {
                        message: msg,
                        url: url,
                        line: line,
                        column: col,
                        error: error
                    });
                    // 显示错误信息在页面上
                    const errorDiv = document.createElement('div');
                    errorDiv.style.color = 'red';
                    errorDiv.style.padding = '20px';
                    errorDiv.innerHTML = `Error: ${msg}<br>Line: ${line}<br>Column: ${col}`;
                    document.body.appendChild(errorDiv);
                };

                // 资源加载错误处理
                window.addEventListener('error', function(e) {
                    if (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') {
                        console.error('Resource loading error:', {
                            element: e.target.tagName,
                            src: e.target.src || e.target.href
                        });
                    }
                }, true);
            </script>
            """
            
            content = content.replace('</head>', f'{debug_script}</head>')

            # 设置响应头
            headers = {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'X-Content-Type-Options': 'nosniff'
            }

            return content, 200, headers

        except Exception as e:
            logger.error(f"Error reading index.html: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Error reading frontend files: {str(e)}'
            }), 500

    except Exception as e:
        logger.error(f"Error in aipainter: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500










# 将资源路由移到外面
@app.route('/api/florence/assets/<path:filename>')
def serve_florence_assets(filename):
    try:
        logger.info(f"Requesting asset: {filename}")
        assets_dir = os.path.join(SCRIPT_DIR, 'florence', 'assets')
        
        if not os.path.exists(assets_dir):
            logger.error(f"Assets directory not found: {assets_dir}")
            return jsonify({'error': 'Assets directory not found'}), 404
        
        file_path = os.path.join(assets_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Asset file not found: {file_path}")
            return jsonify({'error': 'Asset file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.js': 'application/javascript',
            '.css': 'text/css',
            '.wasm': 'application/wasm',
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(assets_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except Exception as e:
        logger.error(f"Error serving asset {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500





@app.route('/api/florence/onnx-community/Florence-2-base-ft/<path:filename>')
def serve_florence_model_files(filename):
    try:
        logger.info(f"Requesting model file: {filename}")
        models_dir = os.path.join(SCRIPT_DIR, 'florence','Florence-2-base-ft')
        
        if not os.path.exists(models_dir):
            logger.error(f"Models directory not found: {models_dir}")
            return jsonify({'error': 'Models directory not found'}), 404
        
        file_path = os.path.join(models_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Model file not found: {file_path}")
            return jsonify({'error': 'Model file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.onnx': 'application/octet-stream',
            '.json': 'application/json',
            '.bin': 'application/octet-stream'
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(models_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'public, max-age=31536000'
        return response
        
    except Exception as e:
        logger.error(f"Error serving model file {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500







    
@app.route('/api/florence/debug')
def debug_florence():
    try:
        dist_dir = os.path.join(SCRIPT_DIR, 'florence')
        assets_dir = os.path.join(dist_dir, 'assets')
        models_dir = os.path.join(SCRIPT_DIR, 'florence', 'models', 'onnx-community', 'Florence-2-base-ft')
        
        debug_info = {
            'paths': {
                'SCRIPT_DIR': SCRIPT_DIR,
                'dist_dir': dist_dir,
                'assets_dir': assets_dir,
                'models_dir': models_dir,
            },
            'exists': {
                'dist_dir': os.path.exists(dist_dir),
                'assets_dir': os.path.exists(assets_dir),
                'index_html': os.path.exists(os.path.join(dist_dir, 'index.html')),
                'models_dir': os.path.exists(models_dir),
            },
            'files': {
                'dist': os.listdir(dist_dir) if os.path.exists(dist_dir) else [],
                'assets': os.listdir(assets_dir) if os.path.exists(assets_dir) else [],
                'models': os.listdir(models_dir) if os.path.exists(models_dir) else [],
            }
        }
        
        return jsonify(debug_info)
        
    except Exception as e:
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500



  
######################################################################################
#NewFun2_paintingboard
######################################################################################


@app.route('/api/paint-board/')
def paintboard():
    increment_tool_visits('paint_board')
    try:
        # 1. 获取并验证参数
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')
        
        logger.info(f"Request parameters - User: {user}")
        
        # 2. 参数验证
        if not user or not key:
            logger.warning("Missing user or key")
            return jsonify({
                'success': False,
                'message': 'Missing user or key'
            }), 401

        # 3. 用户认证
        if not authenticate_user(user, key):
            logger.warning(f"Authentication failed for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid user or key'
            }), 401

        # 4. Token 验证
        if not verify_access_token(user, token, user_key_config):
            logger.warning(f"Token verification failed for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid token'
            }), 401

        # 5. 读取并返回 index.html
        index_path = os.path.join(SCRIPT_DIR, 'dist_board', 'index.html')
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 添加调试脚本
            debug_script = """
            <script>
                // 页面加载完成时的处理
                window.addEventListener('load', function() {
                    console.log('Page loaded');
                    console.log('Root element:', document.getElementById('root'));
                });

                // 错误处理
                window.onerror = function(msg, url, line, col, error) {
                    console.error('Error:', {
                        message: msg,
                        url: url,
                        line: line,
                        column: col,
                        error: error
                    });
                    // 显示错误信息在页面上
                    const errorDiv = document.createElement('div');
                    errorDiv.style.color = 'red';
                    errorDiv.style.padding = '20px';
                    errorDiv.innerHTML = `Error: ${msg}<br>Line: ${line}<br>Column: ${col}`;
                    document.body.appendChild(errorDiv);
                };

                // 资源加载错误处理
                window.addEventListener('error', function(e) {
                    if (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') {
                        console.error('Resource loading error:', {
                            element: e.target.tagName,
                            src: e.target.src || e.target.href
                        });
                    }
                }, true);
            </script>
            """
            
            content = content.replace('</head>', f'{debug_script}</head>')

            # 设置响应头
            headers = {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'X-Content-Type-Options': 'nosniff'
            }

            return content, 200, headers

        except Exception as e:
            logger.error(f"Error reading index.html: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Error reading frontend files: {str(e)}'
            }), 500

    except Exception as e:
        logger.error(f"Error in aipainter: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500










# 将资源路由移到外面
@app.route('/api/paint-board/assets/<path:filename>')
def serve_paintboard_assets(filename):
    try:
        logger.info(f"Requesting asset: {filename}")
        assets_dir = os.path.join(SCRIPT_DIR, 'dist_board', 'assets')
        
        if not os.path.exists(assets_dir):
            logger.error(f"Assets directory not found: {assets_dir}")
            return jsonify({'error': 'Assets directory not found'}), 404
        
        file_path = os.path.join(assets_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Asset file not found: {file_path}")
            return jsonify({'error': 'Asset file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.js': 'application/javascript',
            '.css': 'text/css',
            '.wasm': 'application/wasm',
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(assets_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except Exception as e:
        logger.error(f"Error serving asset {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500


# 修改调试路由以包含示例图片信息
@app.route('/api/paint-board/debug')
def debug_paintboard():
    try:
        dist_dir = os.path.join(SCRIPT_DIR, 'dist_board')
        assets_dir = os.path.join(dist_dir, 'assets')    
        debug_info = {
            'paths': {
                'SCRIPT_DIR': SCRIPT_DIR,
                'dist_dir': dist_dir,
                'assets_dir': assets_dir,
            },
            'exists': {
                'dist_dir': os.path.exists(dist_dir),
                'assets_dir': os.path.exists(assets_dir),
                'index_html': os.path.exists(os.path.join(dist_dir, 'index.html')),
            },
            'files': {
                'dist': os.listdir(dist_dir) if os.path.exists(dist_dir) else [],
                'assets': os.listdir(assets_dir) if os.path.exists(assets_dir) else [],
            }
        }
        
        return jsonify(debug_info)
        
    except Exception as e:
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

######################################################################################
#NewFun1
######################################################################################
@app.route('/api/aiinpaint/')
def aipainter():
    increment_tool_visits('aiinpaint')
    update_config_if_needed()
    try:
        # 1. 获取并验证参数
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')
        
        logger.info(f"Request parameters - User: {user}")
        
        # 2. 参数验证
        if not user or not key:
            logger.warning("Missing user or key")
            return jsonify({
                'success': False,
                'message': 'Missing user or key'
            }), 401

        # 3. 用户认证
        if not authenticate_user(user, key):
            logger.warning(f"Authentication failed for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid user or key'
            }), 401

        # 4. Token 验证
        if not verify_access_token(user, token, user_key_config):
            logger.warning(f"Token verification failed for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid token'
            }), 401

        # 5. 读取并返回 index.html
        index_path = os.path.join(SCRIPT_DIR, 'dist', 'index.html')
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 添加调试脚本
            debug_script = """
            <script>
                // 页面加载完成时的处理
                window.addEventListener('load', function() {
                    console.log('Page loaded');
                    console.log('Root element:', document.getElementById('root'));
                });

                // 错误处理
                window.onerror = function(msg, url, line, col, error) {
                    console.error('Error:', {
                        message: msg,
                        url: url,
                        line: line,
                        column: col,
                        error: error
                    });
                    // 显示错误信息在页面上
                    const errorDiv = document.createElement('div');
                    errorDiv.style.color = 'red';
                    errorDiv.style.padding = '20px';
                    errorDiv.innerHTML = `Error: ${msg}<br>Line: ${line}<br>Column: ${col}`;
                    document.body.appendChild(errorDiv);
                };

                // 资源加载错误处理
                window.addEventListener('error', function(e) {
                    if (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') {
                        console.error('Resource loading error:', {
                            element: e.target.tagName,
                            src: e.target.src || e.target.href
                        });
                    }
                }, true);
            </script>
            """
            
            content = content.replace('</head>', f'{debug_script}</head>')

            # 设置响应头
            headers = {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'X-Content-Type-Options': 'nosniff'
            }

            return content, 200, headers

        except Exception as e:
            logger.error(f"Error reading index.html: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Error reading frontend files: {str(e)}'
            }), 500

    except Exception as e:
        logger.error(f"Error in aipainter: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500










# 将资源路由移到外面
@app.route('/api/aiinpaint/assets/<path:filename>')
def serve_inpaint_assets(filename):
    try:
        logger.info(f"Requesting asset: {filename}")
        assets_dir = os.path.join(SCRIPT_DIR, 'dist', 'assets')
        
        if not os.path.exists(assets_dir):
            logger.error(f"Assets directory not found: {assets_dir}")
            return jsonify({'error': 'Assets directory not found'}), 404
        
        file_path = os.path.join(assets_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Asset file not found: {file_path}")
            return jsonify({'error': 'Asset file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.js': 'application/javascript',
            '.css': 'text/css',
            '.wasm': 'application/wasm',
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(assets_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except Exception as e:
        logger.error(f"Error serving asset {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500



@app.route('/api/aiinpaint/model/<path:filename>')
def serve_aiinpaint_model_files(filename):
    try:
        logger.info(f"Requesting model file: {filename}")
        models_dir = os.path.join(SCRIPT_DIR, 'dist','model')
        
        if not os.path.exists(models_dir):
            logger.error(f"Models directory not found: {models_dir}")
            return jsonify({'error': 'Models directory not found'}), 404
        
        file_path = os.path.join(models_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Model file not found: {file_path}")
            return jsonify({'error': 'Model file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.onnx': 'application/octet-stream',
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(models_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'public, max-age=31536000'
        return response
        
    except Exception as e:
        logger.error(f"Error serving model file {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/aiinpaint/dist/<path:filename>')
def serve_aiinpaint_dist_files(filename):
    try:
        logger.info(f"Requesting asset: {filename}")
        assets_dir = os.path.join(SCRIPT_DIR, 'dist', 'dist')
        
        if not os.path.exists(assets_dir):
            logger.error(f"Assets directory not found: {assets_dir}")
            return jsonify({'error': 'Assets directory not found'}), 404
        
        file_path = os.path.join(assets_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Asset file not found: {file_path}")
            return jsonify({'error': 'Asset file not found'}), 404
        
        mime_types = {
            '.js': 'application/javascript',
            '.map': 'application/json',  # 修改为正确的 MIME 类型
            '.wasm': 'application/wasm',
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(assets_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except Exception as e:
        logger.error(f"Error serving asset {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/aiinpaint/lib/<path:filename>')
def serve_aiinpaint_lib_files(filename):
    try:
        logger.info(f"Requesting lib file: {filename}")
        lib_dir = os.path.join(SCRIPT_DIR, 'dist', 'lib')
        
        if not os.path.exists(lib_dir):
            logger.error(f"Lib directory not found: {lib_dir}")
            return jsonify({'error': 'Lib directory not found'}), 404
        
        file_path = os.path.join(lib_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Lib file not found: {file_path}")
            return jsonify({'error': 'Lib file not found'}), 404
        
        # 扩展 MIME 类型映射
        mime_types = {
            '.js': 'application/javascript',
            '.map': 'application/json',
            '.wasm': 'application/wasm',
            '.ts': 'application/typescript',
            '.tsx': 'application/typescript',
            '.jsx': 'application/javascript',
            '.json': 'application/json'
        }
        ext = os.path.splitext(filename)[1]
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(lib_dir, filename)
        response.headers['Content-Type'] = mimetype
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        
        # 添加 CORS 头
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        
        return response
        
    except Exception as e:
        logger.error(f"Error serving lib file {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500
# 添加示例图片路由
@app.route('/api/aiinpaint/examples/<path:filename>')
def serve_example_images(filename):
    try:
        logger.info(f"Requesting example image: {filename}")
        # 检查两个可能的示例图片目录
        examples_dir = os.path.join(SCRIPT_DIR, 'dist', 'examples')
        if not os.path.exists(examples_dir):
            examples_dir = os.path.join(SCRIPT_DIR, 'dist', 'assets', 'examples')
        
        if not os.path.exists(examples_dir):
            logger.error(f"Examples directory not found: {examples_dir}")
            return jsonify({'error': 'Examples directory not found'}), 404
        
        file_path = os.path.join(examples_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Example file not found: {file_path}")
            return jsonify({'error': 'Example file not found'}), 404
        
        # 设置正确的 MIME 类型
        mime_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp'
        }
        ext = os.path.splitext(filename)[1].lower()
        mimetype = mime_types.get(ext, 'application/octet-stream')
        
        response = send_from_directory(examples_dir, filename)
        response.headers['Content-Type'] = mimetype
        # 允许示例图片缓存
        response.headers['Cache-Control'] = 'public, max-age=3600'
        return response
        
    except Exception as e:
        logger.error(f"Error serving example image {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

# 修改调试路由以包含示例图片信息
@app.route('/api/aiinpaint/debug')
def debug_inpaint():
    try:
        dist_dir = os.path.join(SCRIPT_DIR, 'dist')
        assets_dir = os.path.join(dist_dir, 'assets')
        examples_dir = os.path.join(dist_dir, 'examples')
        assets_examples_dir = os.path.join(assets_dir, 'examples')
        
        debug_info = {
            'paths': {
                'SCRIPT_DIR': SCRIPT_DIR,
                'dist_dir': dist_dir,
                'assets_dir': assets_dir,
                'examples_dir': examples_dir,
                'assets_examples_dir': assets_examples_dir
            },
            'exists': {
                'dist_dir': os.path.exists(dist_dir),
                'assets_dir': os.path.exists(assets_dir),
                'index_html': os.path.exists(os.path.join(dist_dir, 'index.html')),
                'examples_dir': os.path.exists(examples_dir),
                'assets_examples_dir': os.path.exists(assets_examples_dir)
            },
            'files': {
                'dist': os.listdir(dist_dir) if os.path.exists(dist_dir) else [],
                'assets': os.listdir(assets_dir) if os.path.exists(assets_dir) else [],
                'examples': os.listdir(examples_dir) if os.path.exists(examples_dir) else [],
                'assets_examples': os.listdir(assets_examples_dir) if os.path.exists(assets_examples_dir) else []
            }
        }
        
        return jsonify(debug_info)
        
    except Exception as e:
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500
######################################################################################
#NewFun
######################################################################################
@app.route('/api/ai-demo/')
def ai_demo():
    update_config_if_needed()
    increment_tool_visits('ai_demo')
    logger.info("AI Demo route accessed")
    logger.info(f"Script directory: {SCRIPT_DIR}")
    
    try:
        # 1. 获取并记录所有请求参数
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')
        
        logger.info(f"Request parameters - User: {user}, Key length: {len(key) if key else 0}, Token length: {len(token) if token else 0}")

        # 2. 参数验证
        if not user or not key:
            logger.warning("Missing user or key")
            return jsonify({
                'success': False,
                'message': 'Missing user or key'
            }), 401

        # 3. 用户认证
        try:
            auth_result = authenticate_user(user, key)
            logger.info(f"Authentication result for user {user}: {auth_result}")
            if not auth_result:
                return jsonify({
                    'success': False,
                    'message': 'Invalid user or key'
                }), 401
        except Exception as auth_error:
            logger.error(f"Authentication error: {str(auth_error)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'Authentication error: {str(auth_error)}'
            }), 500

        # 4. Token 验证
        try:
            token_valid = verify_access_token(user, token, user_key_config)
            logger.info(f"Token verification result for user {user}: {token_valid}")
            if not token_valid:
                return jsonify({
                    'success': False,
                    'message': 'Invalid or expired token'
                }), 401
        except Exception as token_error:
            logger.error(f"Token verification error: {str(token_error)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'Token verification error: {str(token_error)}'
            }), 500
            
        # 5. 文件路径验证
        html_path = os.path.join(SCRIPT_DIR, 'removebackground','index.html')
        logger.info(f"HTML file path: {html_path}")
        logger.info(f"File exists: {os.path.exists(html_path)}")
        
        if not os.path.exists(html_path):
            logger.error(f"HTML file not found at path: {html_path}")
            return jsonify({
                'success': False,
                'message': 'HTML file not found'
            }), 404
            
        # 6. 文件读取
        try:
            logger.info("Reading HTML file")
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            logger.info(f"HTML file read successfully, content length: {len(html_content)}")
            
            # 7. 返回 HTML 内容
            return html_content, 200, {'Content-Type': 'text/html; charset=utf-8'}
            
        except Exception as file_error:
            logger.error(f"Error reading HTML file: {str(file_error)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'Error reading HTML file: {str(file_error)}'
            }), 500
        
    except Exception as e:
        logger.error(f"Unexpected error in ai_demo route: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'Unexpected error: {str(e)}'
        }), 500

@app.route('/api/ai-demo/assets/<path:filename>')
def serve_demo_asset(filename):
    logger.info(f"=== Starting serve_demo_asset for file: {filename} ===")
    
    try:
        # 安全检查文件名
        filename = secure_filename(filename)
        logger.info(f"Secured filename: {filename}")
        
        # 构建资源文件的完整路径
        assets_dir = Path(SCRIPT_DIR) / 'removebackground' / 'assets'
        file_path = assets_dir / filename
        
        logger.info(f"Assets directory: {assets_dir}")
        logger.info(f"Full file path: {file_path}")
        logger.info(f"File exists: {file_path.exists()}")
        
        # 检查目录和文件
        if not assets_dir.exists():
            logger.error(f"Assets directory does not exist: {assets_dir}")
            return jsonify({
                'error': 'Assets directory not found',
                'path': str(assets_dir)
            }), 500
            
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return jsonify({
                'error': 'File not found',
                'path': str(file_path)
            }), 404
            
        # 检查文件权限
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
        except PermissionError:
            logger.error(f"Permission denied reading file: {file_path}")
            return jsonify({
                'error': 'Permission denied',
                'path': str(file_path)
            }), 403
        except Exception as e:
            logger.error(f"Error reading file: {str(e)}")
            return jsonify({
                'error': f'File read error: {str(e)}',
                'path': str(file_path)
            }), 500
            
        # 设置 MIME 类型
        mime_types = {
            '.js': 'application/javascript',
            '.mjs': 'application/javascript',
            '.css': 'text/css',
        }
        
        content_type = mime_types.get(file_path.suffix.lower(), 'application/octet-stream')
        logger.info(f"Content type: {content_type}")
        
        # 返回文件内容
        response = app.make_response(file_content)
        response.headers['Content-Type'] = content_type
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Cache-Control'] = 'public, max-age=31536000'
        
        logger.info("Successfully serving file")
        return response
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            'error': f'Server error: {str(e)}',
            'type': str(type(e))
        }), 500

# 添加调试端点
@app.route('/api/ai-demo/debug/files')
def debug_files():
    try:
        assets_dir = Path(SCRIPT_DIR) / 'removebackground' / 'assets'
        return jsonify({
            'script_dir': str(SCRIPT_DIR),
            'assets_dir': str(assets_dir),
            'assets_dir_exists': assets_dir.exists(),
            'assets_dir_is_dir': assets_dir.is_dir() if assets_dir.exists() else None,
            'assets_contents': [str(f.name) for f in assets_dir.iterdir()] if assets_dir.exists() and assets_dir.is_dir() else [],
            'js_file': {
                'path': str(assets_dir / 'index-17_Va4sS.js'),
                'exists': (assets_dir / 'index-17_Va4sS.js').exists(),
                'size': (assets_dir / 'index-17_Va4sS.js').stat().st_size if (assets_dir / 'index-17_Va4sS.js').exists() else None,
                'permissions': oct((assets_dir / 'index-17_Va4sS.js').stat().st_mode)[-3:] if (assets_dir / 'index-17_Va4sS.js').exists() else None
            },
            'css_file': {
                'path': str(assets_dir / 'index-SojaDS6z.css'),
                'exists': (assets_dir / 'index-SojaDS6z.css').exists(),
                'size': (assets_dir / 'index-SojaDS6z.css').stat().st_size if (assets_dir / 'index-SojaDS6z.css').exists() else None,
                'permissions': oct((assets_dir / 'index-SojaDS6z.css').stat().st_mode)[-3:] if (assets_dir / 'index-SojaDS6z.css').exists() else None
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    


 
# 如果之前有使用 Flask-CORS，先移除它
# from flask_cors import CORS
# CORS(app)

# 添加全局 CORS 处理
@app.after_request
def after_request(response):
    # 移除可能存在的旧 CORS 头
    for header in ['Access-Control-Allow-Origin', 'Access-Control-Allow-Methods', 
                  'Access-Control-Allow-Headers', 'Access-Control-Max-Age']:
        if header in response.headers:
            del response.headers[header]
    
    # 设置新的 CORS 头
    response.headers['Access-Control-Allow-Origin'] = 'http://localhost:5173'
    response.headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
    return response

@app.route('/api/ai-demo/model/<path:filename>')
def serve_aidemo_model_files(filename):
    try:
        logger.info(f"Requesting model file: {filename}")
        models_dir = os.path.join(SCRIPT_DIR, 'dist', 'model', 'onnx')
        
        if not os.path.exists(models_dir):
            logger.error(f"Models directory not found: {models_dir}")
            return jsonify({'error': 'Models directory not found'}), 404
        
        file_path = os.path.join(models_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Model file not found: {file_path}")
            return jsonify({'error': 'Model file not found'}), 404
        
        response = send_from_directory(models_dir, filename)
        response.headers.update({
            'Content-Type': 'application/octet-stream',
            'Cache-Control': 'public, max-age=31536000'
        })
        # 不在这里设置 CORS 头，让全局处理器处理
        logger.info(f"Successfully served model file: {filename}")
        return response
        
    except Exception as e:
        logger.error(f"Error serving model file {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

# OPTIONS 请求处理
@app.route('/api/ai-demo/model/<path:filename>', methods=['OPTIONS'])
def handle_preflight(filename):
    response = app.make_response('')
    # 不需要在这里设置 CORS 头，让全局处理器处理
    return response


######################################################################################
#VIP设置
######################################################################################
@app.route('/api/set_vip', methods=['POST'])
def set_vip():
    update_config_if_needed()
    try:
        data = request.json
        username = data.get('username', '')
        vip_expiry = data.get('vip_expiry','')  # 改为 vip_expiry
        admin_username = data.get('admin_username', '')
        admin_token = data.get('admin_token', '')
        admin_key = data.get('admin_key', '')



        if not authenticate_user(admin_username, admin_key):
            return jsonify({'success': False, 'message': 'Invalid admin user or key'}), 401

        # 验证管理员身份
        if not verify_access_token(admin_username, admin_token, user_key_config):
            return jsonify({'success': False, 'message': 'Unauthorized'}), 401
            
        # 确保是管理员用户
        if admin_username != 'admin':
            return jsonify({'success': False, 'message': 'Only admin can set VIP status'}), 403

        if not username or not vip_expiry:  # 改为 vip_expiry
            return jsonify({'success': False, 'message': 'Missing required data'}), 400

        # 转换时间字符串为时间戳
        vip_expiry_timestamp = datetime.strptime(vip_expiry, '%Y-%m-%dT%H:%M').timestamp()  # 改为 vip_expiry


        if username not in user_key_config['credentials']['usernames']:
            return jsonify({'success': False, 'message': 'User not found'}), 404

        # 添加或更新 VIP 信息
        user_key_config['credentials']['usernames'][username]['vip_expiry'] = vip_expiry_timestamp  # 保持一致的命名

        # 保存更新后的配置
        with open(user_key_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(user_key_config, f, allow_unicode=True)

        return jsonify({'success': True, 'message': 'VIP status updated successfully'})

    except Exception as e:
        logger.error(f"Error setting VIP status: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500








######################################################################################
def verify_access_token(username, token, config):
    """验证访问令牌是否有效

    Args:
        username (str): 用户名
        token (str): 访问令牌
        config (dict): 用户配置信息

    Returns:
        bool: 令牌是否有效
    """
    try:
        # 确保配置是最新的
        update_config_if_needed()
        global user_key_config
        config = user_key_config  # 使用最新的配置

        # 记录验证开始
        logger.info(f"Starting token verification for user: {username}")

        # 确保配置文件结构正确
        if not config or 'credentials' not in config or 'usernames' not in config['credentials']:
            logger.error("Invalid config structure")
            return False

        # 确保用户存在
        if username not in config['credentials']['usernames']:
            logger.error(f"User not found: {username}")
            return False

        user_info = config['credentials']['usernames'][username]

        # 检查token和expiry字段是否存在
        if 'token' not in user_info:
            logger.error(f"Token field not found for user: {username}")
            return False

        if 'expiry' not in user_info:
            logger.error(f"Expiry field not found for user: {username}")
            return False

        # 检查token是否匹配且未过期
        current_time = int(time.time())  # 转换为整数以避免浮点数比较问题
        expiry_time = int(float(user_info['expiry']))  # 确保expiry也是整数

        # 详细记录验证信息
        logger.info(
            f"Token verification details:\n"
            f"- User: {username}\n"
            f"- Stored token: {user_info['token']}\n"
            f"- Provided token: {token}\n"
            f"- Current time: {current_time}\n"
            f"- Expiry time: {expiry_time}\n"
            f"- Time remaining: {expiry_time - current_time} seconds"
        )

        # 分别检查token匹配和过期时间，并记录结果
        token_matches = user_info['token'] == token
        not_expired = current_time <= expiry_time

        if not token_matches:
            logger.error(f"Token mismatch for user: {username}")
        if not not_expired:
            logger.error(f"Token expired for user: {username}")

        return token_matches and not_expired

    except Exception as e:
        logger.error(f"Token verification error for user {username}: {str(e)}")
        logger.exception("Detailed error traceback:")
        return False

#######################################################################
#files_chat
#######################################################################
# ... 其他导入和设置 ...


def load_user_config(username):
    with open('user_configs.json', 'r', encoding='utf-8') as f:
        configs = json.load(f)
    return configs.get(username, {})

def check_and_update_filechat_count(user, method):
    # 检查用户的 VIP 状态
    is_vip = False
    if user in user_key_config['credentials']['usernames']:
        user_info = user_key_config['credentials']['usernames'][user]
        if 'vip_expiry' in user_info:
            expiry_timestamp = user_info['vip_expiry']
            if expiry_timestamp > time.time():  # VIP 未过期
                is_vip = True

    counts = load_user_counts()

    if user not in counts:
        counts[user] = {'filechat': 1}
    elif 'filechat' not in counts[user]:
        counts[user]['filechat'] = 1
    else:
        counts[user]['filechat'] += 1

    save_user_counts(counts)

    # 根据用户身份和方法确定对话限制
    if user == "admin":
        return 100  # 管理员无限制，计数用

    # 基础限制
    adjusted_limit = DAILY_FILECHAT_LIMIT
    
    # 根据方法增加限制
    if method == 1 or method == 3:
        adjusted_limit += 50
    
    # VIP 用户额外增加基础限制次数
    if is_vip:
        adjusted_limit += vip_add_filechat_limit

    return adjusted_limit - counts[user].get('filechat', 0)




##################################################################################################
#filechat
##################################################################################################

def to_bool(val):
    if isinstance(val, bool):
        return val
    if isinstance(val, str):
        return val.lower() == "true"
    if isinstance(val, int):
        return val != 0
    return False




@app.route('/api/filechat', methods=['POST'])
def handle_filechat():
    data = request.json
    user = data.get('user')
    key = data.get('key')
    message = data.get('message')
    chatfile_history=data.get('history',"")
    request_id = str(uuid.uuid4())[:8]  # 生成请求ID
    log_with_request_id(f"Message:{message[:500]}", request_id=request_id)                
    if not authenticate_user(user, key):
        return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
    
    user_config = load_user_config(user)
    
    # 从用户配置中获取 process_query 所需的参数
    method = user_config.get('method', 3)  # 默认值为3
    net_method = user_config.get('net_method', 'duckduckgo')
    query_source = user_config.get('query_source', '用户输入')
    deep_search_enabled = user_config.get('deep_search_enabled', False)
    api_key_deepseek = user_config.get('api_key_deepseek', '')
    api_key_siliconflow = user_config.get('api_key_siliconflow', '')
    api_key_qwen = user_config.get('api_key_qwen', '')
    api_key_type = user_config.get('api_key_type', '')
    api_key = api_key_deepseek if api_key_type == "deepseek" else api_key_siliconflow if api_key_type == "siliconflow" else api_key_qwen if api_key_type == "qwen" else ""
    language = user_config.get('language', 'Chinese')
    summary_length = user_config.get('summary_length', 500)
    max_results = user_config.get('max_results', 5)
    selected_doc_ids = user_config.get('selected_doc_ids', '')
    log_with_request_id(f"知识库文件有1：{selected_doc_ids}",request_id=request_id)
    session_id = user_config.get('conversation_id', '')
    api_key_rag = user_config.get('api_key_rag', '')
    enable_thinking =user_config.get('enable_thinking', False)
    chat_id_test="88a31ba86e1011efa2e10242ac120002" #test_chat
    remaining_chats = check_and_update_filechat_count(user, method)
    log_with_request_id(f"是否启用enable_thinking: {enable_thinking}",request_id=request_id)
    # 记录关键参数
    log_with_request_id(
        f"Parameters - method: {method}, net_method: {net_method}, deep_search_enabled: {deep_search_enabled}, "
        f"query_source: {query_source}, api_key_lengths: "
        f"deepseek({len(api_key_deepseek) if api_key_deepseek else 0}), "
        f"rag({len(api_key_rag) if api_key_rag else 0})",
        request_id=request_id
    )

    if remaining_chats < 0 and user != "admin":
        return jsonify({
            'success': False, 
            'message': 'Daily filechat limit exceeded',
            'remainingChats': 0
        }), 429

    try:
        response = process_query(method, deep_search_enabled, net_method, query_source, api_key, api_key_type, language, 
                                 summary_length, selected_doc_ids, session_id, chat_id_test, api_key_rag, message, chatfile_history, max_results, enable_thinking)
        log_with_request_id(f"Query processed successfully", request_id=request_id)
        log_with_request_id(f"Response contentserver: {response[:500]}..." if len(response) > 500 else f"Response content: {response}", request_id=request_id)

        # 确保响应是字符串格式
        if not isinstance(response, str):
            response = str(response)
        logger.info(f"Remaining filechat count: {remaining_chats}")


        # 生成唯一 filechat 序号，格式 filechat_yyyymmddHHMMSSmmm_随机8位
        now = datetime.now()
        timestamp = now.strftime('%Y%m%d%H%M%S%f')[:-3]  # 精确到毫秒
        random_part = uuid.uuid4().hex[:8]
        chat_seq = f"filechat_{timestamp}_{random_part}"

        # 获取用户class
        user_class = authenticate_user_class(user) if 'authenticate_user_class' in globals() else ""

        # 记录 filechat 日志
        log_dir = os.path.join(os.path.dirname(__file__), 'chat_logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        log_date = now.strftime('%Y%m%d')
        log_path = os.path.join(log_dir, f'{log_date}.json')

        log_record = {
            'user': user,
            'user_class': user_class if user_class else "",
            'time': now.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
            'chat_seq': chat_seq,
            'message': message,
            'response': response,
            'filename': "",  # filechat没有filename参数时可留空
            'method': method,
            'api_key_type': api_key_type,
            'api_key': api_key
        }

        try:
            if os.path.exists(log_path):
                with open(log_path, 'r', encoding='utf-8') as f:
                    all_logs = json.load(f)
            else:
                all_logs = {}
            all_logs[chat_seq] = log_record
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump(all_logs, f, ensure_ascii=False, indent=2)
        except Exception as logerr:
            log_with_request_id(f"Failed to write filechat log: {logerr}", request_id=request_id)
        return jsonify({
            'success': True,
            'response': response,
            'remainingChats': remaining_chats,
            'message': 'Success'  # 添加统一的消息字段
        })
    except Exception as e:
        logger.error(f"Error in chat: {str(e)}")
        print(f"Error in chat: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e),  # 返回具体的错误信息
            'remainingChats': remaining_chats,
            'error': 'An error occurred during the chat'
        }), 500




#######################################################################
#blog_chat
#######################################################################


def load_user_counts():
    try:
        if os.path.exists(USER_UPLOAD_COUNTS_FILE):
            with open(USER_UPLOAD_COUNTS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {}
    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from {USER_UPLOAD_COUNTS_FILE}. File may be corrupted.")
        return {}
    except Exception as e:
        logger.error(f"Error loading user counts: {str(e)}")
        return {}

def save_user_counts(counts):
    with open(USER_UPLOAD_COUNTS_FILE, 'w', encoding='utf-8') as f:
        json.dump(counts, f, indent=2)

def check_and_update_chat_count(user):
    # 检查用户的 VIP 状态
    is_vip = False
    if user in user_key_config['credentials']['usernames']:
        user_info = user_key_config['credentials']['usernames'][user]
        if 'vip_expiry' in user_info:
            expiry_timestamp = user_info['vip_expiry']
            if expiry_timestamp > time.time():  # VIP 未过期
                is_vip = True

    counts = load_user_counts()

    if user not in counts:
        counts[user] = {'chat': 1}
    elif 'chat' not in counts[user]:
        counts[user]['chat'] = 1
    else:
        counts[user]['chat'] += 1

    save_user_counts(counts)

    # 根据用户身份确定对话限制
    if user == "admin":
        return 100 - counts[user].get('chat', 0)  # 管理员无限制，仅计数
    
    # VIP 用户获得额外的对话次数
    adjusted_limit = DAILY_CHAT_LIMIT
    if is_vip:
        adjusted_limit += vip_add_chat_limit

    return adjusted_limit - counts[user].get('chat', 0)





@app.route('/api/chat', methods=['POST'])
def handle_chat():
    update_config_if_needed()
    # 记录请求开始    
    request_id = str(uuid.uuid4())[:8]  # 生成请求ID
    log_with_request_id(f"Starting new chat request", request_id=request_id)
    try:
        # 记录请求数据
        data = request.json
        log_with_request_id(f"Received request data: {data}", request_id=request_id)
        
        # 获取并记录各个参数
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '')
        message = data.get('message', '')
        conversation_id = data.get('conversationId', '1')
        filename = data.get('filename', '')
        doc_id = data.get('doc_id', '')
        history = data.get('history', '')
        dataset_id = data.get('dataset_id', '')
        folder = data.get('folder', '')
        # 生成唯一聊天序号，格式 chat_yyyymmddHHMMSSmmm_随机8位
        now = datetime.now()
        timestamp = now.strftime('%Y%m%d%H%M%S%f')[:-3]  # 精确到毫秒
        random_part = uuid.uuid4().hex[:8]
        chat_seq = f"chat_{timestamp}_{random_part}"
        log_with_request_id(f"Generated chat_seq: {chat_seq}", request_id=request_id)
        # 处理dataset_id和doc_id，确保它们是有效的格式
        # 如果是字符串，转换为列表
        if dataset_id and isinstance(dataset_id, str):
            # 检查是否是逗号分隔的多个ID
            if ',' in dataset_id:
                dataset_id = [id.strip() for id in dataset_id.split(',')]
            else:
                dataset_id = [dataset_id]
        elif not dataset_id:
            dataset_id = []
            
        if doc_id and isinstance(doc_id, str):
            # 检查是否是逗号分隔的多个ID
            if ',' in doc_id:
                doc_id = [id.strip() for id in doc_id.split(',')]
            else:
                doc_id = [doc_id]
        elif not doc_id:
            doc_id = []
            
        log_with_request_id(f"Processed dataset_id: {dataset_id}", request_id=request_id)
        log_with_request_id(f"Processed doc_id: {doc_id}", request_id=request_id)

        # 构建并检查博客路径
        blog_path = os.path.join(BLOG_DIR, filename)
        log_with_request_id(f"Blog path: {blog_path}", request_id=request_id)
        log_with_request_id(f"Blog path exists: {os.path.exists(blog_path)}", request_id=request_id)
        
        # 加载用户配置
        user_config = load_user_config(user)
        log_with_request_id(f"Loaded user config: {user_config}", request_id=request_id)
        api_key_siliconflow = user_config.get('api_key_siliconflow', '')
        api_key_deepseek = user_config.get('api_key_deepseek', '')
        api_key_qwen = user_config.get('api_key_qwen', '')
        api_key_type = user_config.get('api_key_type', '')
        api_key = api_key_deepseek if api_key_type == "deepseek" else api_key_siliconflow if api_key_type == "siliconflow" else api_key_qwen if api_key_type == "qwen" else ""
        api_key_rag = user_config.get('api_key_rag', '')   
        # 获取用户class值


        user_class = authenticate_user_class(user)
        # 如果存在class值，记录用户数据
        if user_class:
            log_with_request_id(f"User {user} has class: {user_class},filename is: {filename}", request_id=request_id)
            record_class_user_data(user, user_class, message, filename)

        # 处理文档ID - 只有在没有提供doc_id和dataset_id时才尝试上传
        if not doc_id and filename:
            log_with_request_id("No doc_id or dataset_id provided, attempting to upload or get doc_id", request_id=request_id)
            new_doc_id = upload_or_get_doc_id(blog_path, kb_id_blog, api_key_blog, parser_id="naive")
            log_with_request_id(f"Retrieved doc_id: {new_doc_id}", request_id=request_id)
            if new_doc_id:
                doc_id = [new_doc_id]
            else:
                logger.error("Failed to get doc_id")
                return jsonify({'success': False, 'message': '正在添加知识库，请稍后再试'}), 500
        
        # 验证用户
        if not authenticate_user(user, key):
            log_with_request_id(f"Authentication failed for user: {user}", request_id=request_id)
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
        
        # 检查聊天限制
        remaining_chats = check_and_update_chat_count(user)
        log_with_request_id(f"Remaining chats for user {user}: {remaining_chats}", request_id=request_id)
        if remaining_chats < 0 and user != "admin":
            log_with_request_id(f"Chat limit exceeded for user: {user}", request_id=request_id)
            return jsonify({'success': False, 'message': 'Daily chat limit exceeded'}), 429
        
        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            log_with_request_id(f"Token verification failed for user: {user}", request_id=request_id)
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        # 检查是否有有效的ID
        if not doc_id and not dataset_id:
            log_with_request_id("No valid doc_id or dataset_id provided", level=logging.WARNING, request_id=request_id)
            return jsonify({'success': False, 'message': '请提供有效的文档ID或数据集ID'}), 400
        
        # 处理会话
        try:
            log_with_request_id(f"Created new conversation with ID: {conversation_id}", request_id=request_id)
            # 创建聊天机器人实例
            log_with_request_id("Initializing chatbot", request_id=request_id)
            chatbot = SmartChatbot(
                api_key_rag=api_key_rag,
                api_key=api_key,
                api_key_type=api_key_type,
                folder=folder
            )
            log_with_request_id(f"Initialized chatbot", request_id=request_id)
            # 获取答案
            log_with_request_id(f"Dataset IDs: {dataset_id}, Document IDs: {doc_id}", request_id=request_id)
            response = chatbot.get_answer(
                question=message,
                history=history,
                dataset_ids=dataset_id,
                document_ids=doc_id
            )
            log_with_request_id(f"Successfully got response from chatbot", request_id=request_id)
            log_with_request_id(f"Response content: {response[:100]}...", request_id=request_id)  # 只记录前100个字符
            
            # 检查响应是否包含错误信息
            if response.startswith('抱歉，处理您的问题时出现错误:'):
                error_detail = response.replace('抱歉，处理您的问题时出现错误:', '').strip()
                log_with_request_id(f"Error detected in response: {error_detail}", level=logging.ERROR, request_id=request_id)
                
                # 根据错误类型返回不同的状态码
                if 'dataset_ids' in error_detail or 'document_ids' in error_detail:
                    return jsonify({'success': False, 'message': '资源初始化中，请稍后再试', 'error_detail': error_detail}), 503
                elif 'Invalid argument' in error_detail or 'Errno 22' in error_detail:
                    return jsonify({'success': False, 'message': '系统暂时不可用，请稍后再试', 'error_detail': error_detail}), 503
                else:
                    return jsonify({'success': False, 'message': '处理请求时出错', 'error_detail': error_detail}), 500
###########################################################################################################################################################            
            # 记录聊天日志
            log_dir = os.path.join(os.path.dirname(__file__), 'chat_logs')
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            log_date = now.strftime('%Y%m%d')
            log_path = os.path.join(log_dir, f'{log_date}.json')

            log_record = {
                'user': user,
                'user_class': user_class if user_class else "",
                'time': now.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                'chat_seq': chat_seq,
                'message': message,
                'response': response,
                'filename': filename,
                'api_key_type': api_key_type,
                'api_key': api_key
            }

            # 以 chat_seq 作为 key 存储到 json
            try:
                if os.path.exists(log_path):
                    try:
                        with open(log_path, 'r', encoding='utf-8') as f:
                            all_logs = json.load(f)
                    except (UnicodeDecodeError, json.JSONDecodeError) as e:
                        log_with_request_id(f"Error reading existing log file, creating new one: {e}", request_id=request_id)
                        all_logs = {}
                else:
                    all_logs = {}
                all_logs[chat_seq] = log_record
                with open(log_path, 'w', encoding='utf-8') as f:
                    json.dump(all_logs, f, ensure_ascii=False, indent=2)
            except Exception as logerr:
                log_with_request_id(f"Failed to write chat log: {logerr}", request_id=request_id)

            # 返回成功响应
            return jsonify({
                'success': True,
                'response': response,
                'conversationId': conversation_id,
                'remainingChats': remaining_chats
            })
            
        except Exception as chat_error:
            log_with_request_id(f"Error during chat processing: {str(chat_error)}", request_id=request_id)
            log_with_request_id(f"Traceback: {traceback.format_exc()}", request_id=request_id)
            return jsonify({'success': False, 'message': f'Chat error: {str(chat_error)}'}), 500
            
    except Exception as e:
        log_with_request_id(f"Unexpected error in handle_chat: {str(e)}", request_id=request_id)
        log_with_request_id(f"Traceback: {traceback.format_exc()}", request_id=request_id)
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500
    finally:
        log_with_request_id("=== Completed chat request ===", request_id=request_id)













#######################################################################
#用户blog上传限制
#######################################################################

# 添加新的函数来加载和保存用户上传计数
def load_user_upload_counts():
    try:
        if os.path.exists(USER_UPLOAD_COUNTS_FILE):
            with open(USER_UPLOAD_COUNTS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {}
    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from {USER_UPLOAD_COUNTS_FILE}. File may be corrupted.")
        return {}
    except Exception as e:
        logger.error(f"Error loading user upload counts: {str(e)}")
        return {}

def save_user_upload_counts(counts):
    with open(USER_UPLOAD_COUNTS_FILE, 'w', encoding='utf-8') as f:
        json.dump(counts, f, indent=2)


# 添加检查和更新上传计数的函数
def check_and_update_upload_count(user, upload_type):
    # 获取用户的 VIP 状态
    is_vip = False
    if user in user_key_config['credentials']['usernames']:
        user_info = user_key_config['credentials']['usernames'][user]
        if 'vip_expiry' in user_info:
            expiry_timestamp = user_info['vip_expiry']
            if expiry_timestamp > time.time():  # VIP 未过期
                is_vip = True

    counts = load_user_upload_counts()

    if user not in counts:
        counts[user] = {upload_type: 1}
    elif upload_type not in counts[user]:
        counts[user][upload_type] = 1
    else:
        counts[user][upload_type] += 1

    save_user_upload_counts(counts)

    # 根据用户身份确定上传限制
    if user == "admin":
        return True  # 管理员无限制
        
    # VIP 用户获得额外的 10 次上传机会
    adjusted_limit = DAILY_UPLOAD_LIMITS[upload_type]
    if is_vip:
        adjusted_limit += vip_add_blog_limit

    # 检查是否超过限制
    if counts[user][upload_type] > adjusted_limit:
        return False
    return True




if not os.path.exists(USER_UPLOAD_COUNTS_FILE):
    save_user_upload_counts({})       
#######################################################################
#blog视频上传
#######################################################################
def secure_videofilename_with_extension(filename):
    # 分离文件名和扩展名
    name, ext = os.path.splitext(filename)
    
    # 使用正则表达式保留汉字、字母、数字、下划线和连字符
    name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9_-]', '', name)
    
    # 如果文件名为空，使用一个默认名称
    if not name:
        name = "unnamed_file"
    
    # 添加时间戳
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 重新组合文件名、时间戳和扩展名
    return secure_filename(f"{name}_{timestamp}{ext}")

# 修改上传视频的函数
@app.route('/api/upload_blog_video', methods=['POST'])
def upload_blog_video():
    logger.info("Received blog video upload request")
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    user = request.form.get('user')
    if not user:
        return jsonify({'error': 'User not specified'}), 400
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    if file:
        if not check_and_update_upload_count(user, 'video') and user != "admin":
            return jsonify({'error': 'Daily upload limit exceeded'}), 429
        filename = secure_videofilename_with_extension(file.filename)
        logger.debug(f"Secured filename: {filename}")
        file_path = os.path.join(BLOG_VIDEO_DIR, filename)
        file.save(file_path)
        if not os.path.exists(file_path):
            return jsonify({'error': 'File upload failed'}), 500
        file_size = os.path.getsize(file_path)
        return jsonify({
            'message': 'Blog video uploaded successfully',
            'size': file_size,
            'filename': filename
        }), 200

@app.route('/api/blog_video/<filename>')
def serve_blog_video(filename):
    """提供博客视频文件的流式传输"""
    try:
        # 验证文件存在
        video_path = os.path.join(BLOG_VIDEO_DIR, filename)
        if not os.path.exists(video_path):
            logger.warning(f"Video file not found: {filename}")
            return jsonify({'error': 'Video not found'}), 404

        # 获取文件大小
        file_size = os.path.getsize(video_path)
        
        # 处理范围请求
        range_header = request.headers.get('Range', None)
        if range_header:
            byte1, byte2 = 0, None
            match = re.search(r'bytes=(\d+)-(\d*)', range_header)
            if match:
                groups = match.groups()
                if groups[0]:
                    byte1 = int(groups[0])
                if groups[1]:
                    byte2 = int(groups[1])
            
            if byte2 is None:
                byte2 = file_size - 1
            
            # 限制每个块的大小为8MB
            chunk_size = 8 * 1024 * 1024  # 8MB
            if byte2 - byte1 >= chunk_size:
                byte2 = byte1 + chunk_size - 1
            
            length = byte2 - byte1 + 1
            
            # 记录正常的流媒体传输日志
            logger.info(f"Streaming video {filename}: bytes {byte1}-{byte2}/{file_size}")
            
            # 打开文件并读取指定范围的数据
            with open(video_path, 'rb') as f:
                f.seek(byte1)
                data = f.read(length)
            
            response = make_response(data)
            response.headers.set('Content-Type', mimetypes.guess_type(filename)[0])
            response.headers.set('Content-Range', f'bytes {byte1}-{byte2}/{file_size}')
            response.headers.set('Accept-Ranges', 'bytes')
            response.headers.set('Content-Length', str(length))
            response.headers.set('Cache-Control', 'public, max-age=31536000')
            response.status_code = 206
            
            return response
            
        # 完整文件请求 - 重定向到分块传输
        logger.info(f"Redirecting to ranged request for: {filename}")
        response = make_response()
        response.headers.set('Accept-Ranges', 'bytes')
        response.headers.set('Content-Length', str(file_size))
        response.headers.set('Content-Type', mimetypes.guess_type(filename)[0])
        response.status_code = 206
        return response

    except Exception as e:
        logger.error(f"Error serving video {filename}: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

#######################################################################
#管理员api
#######################################################################

def read_json_file(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():
                logging.warning(f"File {filename} is empty")
                return {}
            return json.loads(content)
    except FileNotFoundError:
        logging.warning(f"File {filename} not found")
        return {}
    except json.JSONDecodeError as e:
        logging.error(f"Error decoding JSON from {filename}: {e}")
        return {}



@app.route('/api/admin_dashboard/')
def admin_dashboard():
    # 验证管理员身份
    update_config_if_needed()
    user = request.args.get('user')
    key = request.args.get('key')
    token = request.args.get('token')
    
    if not user or not key or user != 'admin':
        return jsonify({"error": "Unauthorized"}), 401

    if not verify_access_token(user, token, user_key_config):
        return jsonify({"error": "Invalid or expired token"}), 401

    try:
        # 读取各种用户数据文件
        user_files = read_json_file(os.path.join('user', 'user_files.json'))
        total_activity_counts = read_json_file('total_user_activity_counts.json')
        submit_counts = read_json_file('user_submit_counts.json')
        upload_counts = read_json_file('user_upload_counts.json')
        rag_counts = read_json_file('user_rag_counts.json')
        user_blogs = load_user_blogs()
        user_videos = read_json_file('user_videos.json')

        # 准备用户数据
        users_data = {}
        for username in user_key_config['credentials']['usernames']:
            user_info = user_key_config['credentials']['usernames'][username]
            is_vip = False
            vip_expiry = None
            vip_days_left = None
            
            if 'vip_expiry' in user_info:
                expiry_timestamp = user_info['vip_expiry']
                current_time = time.time()
                if expiry_timestamp > current_time:
                    is_vip = True
                    vip_expiry = datetime.fromtimestamp(expiry_timestamp).strftime('%Y-%m-%d %H:%M')
                    days_left = (expiry_timestamp - current_time) / (24 * 3600)
                    vip_days_left = int(days_left)

            users_data[username] = {
                'submit_count': submit_counts.get(username, {}).get('count', 0),
                'upload_count': upload_counts.get(username, 0),
                'rag_count': rag_counts.get(username, 0),
                'total_activity': total_activity_counts.get(username, 0),
                'rag_files': user_files.get(username, []),
                'blogs': [],
                'videos': user_videos.get(username, []),
                'is_vip': is_vip,
                'vip_expiry': vip_expiry,
                'vip_days_left': vip_days_left
            }
            
            # 添加博客信息
            if username in user_blogs:
                for blog in user_blogs[username]:
                    users_data[username]['blogs'].append({
                        'filename': blog['filename'],
                        'created_at': blog['created_at']
                    })

        # 读取并渲染模板
        with open('admin_dashboard.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        return render_template_string(
            template_content,
            users_data=users_data
        )

    except Exception as e:
        logger.error(f"Error in admin dashboard: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/delete_redundant', methods=['POST'])
def handle_delete_redundant():
    result = delete_redundant_files()
    sync_user_files_with_doc_ids(base_url, api_key, kb_id)
    return jsonify(result), 200

def delete_redundant_files():
    # 设置正确的文件路径
    user_files_path = os.path.join('user', 'user_files.json')
    
    try:
        # 检查文件是否存在
        if not os.path.exists(user_files_path):
            raise FileNotFoundError(f"找不到文件: {user_files_path}")

        # 读取 user_files.json，明确指定 UTF-8 编码
        try:
            with open(user_files_path, 'r', encoding='utf-8') as f:
                user_files = json.load(f)
        except UnicodeDecodeError:
            # 如果 UTF-8 失败，尝试 GBK 编码
            with open(user_files_path, 'r', encoding='gbk') as f:
                user_files = json.load(f)
        
        files_to_delete = []
        updated_user_files = defaultdict(list)

        for user, files in user_files.items():
            # 如果文件数量大于9，只保留最新的9个文件
            if is_user_vip(user):
                if len(files) > 100:
                    kept_files = files[-100:]  # 取最后9个文件
                    files_to_delete.extend([f for f in files if f not in kept_files])
                    print(f"用户 {user} 的文件数量超过100，保留最新的100个文件")
                else:
                    kept_files = files  # 如果文件数量不超过100，保留所有文件
            else:
                if len(files) > 9:
                    kept_files = files[-9:]  # 取最后9个文件
                    files_to_delete.extend([f for f in files if f not in kept_files])
                    print(f"用户 {user} 的文件数量超过9，保留最新的9个文件")
                else:
                    kept_files = files  # 如果文件数量不超过9，保留所有文件
            
            updated_user_files[user] = kept_files

        # 删除文件
        logger.info(f"需要删除的文件数量: {len(files_to_delete)}")
        num_files_to_delete = len(files_to_delete)
        flag_local_files=0
        for file in files_to_delete:
            try:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"已删除: {file}")
                    logger.info(f"已删除: {file}")
                    flag_local_files+=1
                else:   
                    print(f"文件不存在: {file}")
                    logger.info(f"文件不存在: {file}")
            except Exception as e:
                print(f"删除文件 {file} 时出错: {e}")
                logger.info(f"删除文件 {file} 时出错: {e}")
        logger.info(f"已成功删除 {flag_local_files} 个本地文件")
        # 更新 user_files.json，使用 UTF-8 编码 
        with open(user_files_path, 'w', encoding='utf-8') as f:
            json.dump(dict(updated_user_files), f, indent=2)
        logger.info(f"已成功删除 {num_files_to_delete} 个冗余文件")
        return {"message": f"已成功删除 {num_files_to_delete} 个冗余文件,成功本地ragdoc文件{flag_local_files}个", "deleted_files": files_to_delete}

    except Exception as e:
        error_message = f"删除冗余文件时出错: {str(e)}"
        print(error_message)
        return {"error": error_message}




@app.route('/api/delete_video', methods=['POST'])
def delete_video():
    data = request.json
    username = data.get('username')
    video = data.get('video')
    
    if not username or not video:
        return jsonify({'success': False, 'error': '缺少用户名或视频名'}), 400
    
    video_path = os.path.join(VIDEO_DIR, video)
    user_data_path = "user_videos.json"
    
    try:
        # 尝试删除视频文件（如果存在）
        if os.path.exists(video_path):
            os.remove(video_path)
            file_deleted = True
        else:
            file_deleted = False
        
        # 更新用户的视频列表
        record_deleted = False
        if os.path.exists(user_data_path):
            with open(user_data_path, 'r') as f:
                user_data = json.load(f)
            
            if username in user_data and video in user_data[username]:
                user_data[username].remove(video)
                record_deleted = True
            
            with open(user_data_path, 'w', encoding='utf-8') as f:
                json.dump(user_data, f, indent=2)
        
        # 根据操作结果返回相应的消息
        if file_deleted and record_deleted:
            message = "视频文件和记录已成功删除"
        elif file_deleted:
            message = "视频文件已删除，但记录不存在"
        elif record_deleted:
            message = "视频文件不存在，记录已删除"
        else:
            message = "视频文件和记录都不存在"
        
        return jsonify({'success': True, 'message': message}), 200
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500












#######################################################################
#知识图谱api
#######################################################################
# 添加新的路由来处理知识图谱的加载
@app.route('/api/graph/<filename>')
def get_graph(filename):
    graph_path = os.path.join('graph', filename)
    logger.info(f"Attempting to load graph file: {graph_path}")
    
    if os.path.exists(graph_path):
        try:
            with open(graph_path, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.debug(f"File content: {content[:200]}...")  # Log first 200 characters
                graph_data = json.loads(content)
            return jsonify(graph_data)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {str(e)}")
            return jsonify({"error": "Invalid JSON in graph file"}), 500
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return jsonify({"error": "Server error"}), 500
    else:
        logger.warning(f"Graph file not found: {graph_path}")
        return jsonify({"error": "Graph file not found"}), 404
#######################################################################
#搜索api
#######################################################################

@app.route('/api/search_blogs')
def search_blogs():
    try:
        # 更新配置
        update_config_if_needed()        
        search_term = request.args.get('search', '').lower()
        with open('user_blogs.json', 'r') as f:
            user_blogs = json.load(f)
        
        # 读取评论数据
        comments = load_comments()
        all_blogs = []
        for user, blogs in user_blogs.items():
            for blog in blogs:
                blog['user'] = user
                # 保留分类信息
                blog['category'] = blog.get('category', '未分类')  # 确保有分类                
                # 获取最新评论时间
                blog_comments = comments.get(blog['filename'], [])
                if blog_comments:
                    latest_comment_time = max(datetime.fromisoformat(comment['timestamp']) for comment in blog_comments)
                    blog['latest_activity'] = latest_comment_time
                else:
                    # 如果没有评论，使用博客创建时间
                    blog['latest_activity'] = datetime.fromisoformat(blog['created_at'])
                all_blogs.append(blog)
        
        # 按最新活动时间排序（最新评论时间或创建时间）
        all_blogs.sort(key=lambda x: x['latest_activity'], reverse=True)
        
        # 搜索和过滤博客
        filtered_blogs = []
        for blog in all_blogs:
            file_path = os.path.join('blogs', blog['filename'])
            if not os.path.exists(file_path):
                logger.warning(f"Blog file not found: {file_path}")
                continue  # 跳过不存在的文件
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except IOError as e:
                logger.error(f"Error reading blog file {file_path}: {e}")
                continue  # 如果无法读取文件，跳过这个博客
            
            # 获取标题
            title = content.split('\n')[0].lstrip('# ')
            blog['title'] = title
            
            # 搜索匹配
            if not search_term or search_term in title.lower() or search_term in content.lower():
                # 查找第一张图片（HTML 或 Markdown 格式）
                img_src = None
                img_alt = '博客图片'
                
                # 先尝试查找 HTML 格式的图片
                soup = BeautifulSoup(content, 'html.parser')
                img_tag = soup.find('img')
                if img_tag:
                    img_src = img_tag.get('src', '')
                    img_alt = img_tag.get('alt', '博客图片')
                
                # 如果没有找到 HTML 格式的图片，尝试查找 Markdown 格式的图片
                if not img_src:
                    md_img_match = re.search(r'!\[([^\]]*)\]\(([^)\s]+)', content)
                    if md_img_match:
                        img_alt = md_img_match.group(1) or '博客图片'
                        img_src = md_img_match.group(2)
                
                if img_src:
                    blog['first_image'] = {
                        'src': img_src,
                        'alt': img_alt,
                        'width': '80'  # 默认宽度为80
                    }
                
                # 添加评论信息
                blog['comments'] = comments.get(blog['filename'], [])
                
                filtered_blogs.append(blog)
        
        # 获取博客统计信息
        stats = load_views()
        response = make_response(jsonify({"success": True, "blogs": filtered_blogs, "stats": stats}))
        # 设置 Cache-Control 和 Expires 头
        response.headers['Cache-Control'] = 'public, max-age=30'
        response.headers['Expires'] = (datetime.now(timezone.utc) + timedelta(seconds=30)).strftime("%a, %d %b %Y %H:%M:%S GMT")
        

        return response
    except Exception as e:
        logger.error(f"Error processing request: {e}")
        return jsonify({"success": False, "error": "Internal Server Error"}), 500

#####################################################
#附件api
#####################################################
def allowed_attachment(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_ATTACHMENT_EXTENSIONS


def authenticate_user(user, key):
    # 确保配置是最新的
    update_config_if_needed()
    global user_key_config

    if user in user_key_config['credentials']['usernames']:
        stored_password = user_key_config['credentials']['usernames'][user]['password']
        return stored_password == key
    return False



def authenticate_user_class(user):
    if user in user_key_config['credentials']['usernames']:
        # Check if 'class' exists in user data and is not None/empty
        if 'class' in user_key_config['credentials']['usernames'][user] and user_key_config['credentials']['usernames'][user]['class']:
            class_name = user_key_config['credentials']['usernames'][user]['class']
            return class_name
    return False



def secure_filename_with_chinese(filename):
    filename = unquote(filename)
    filename = filename.replace(' ', '_')
    filename = re.sub(r'[^\w\.\-\u4e00-\u9fff]', '', filename)
    filename = filename.strip('._')
    return filename

@app.route('/api/upload_attachment', methods=['POST'])
def upload_attachment():
    """处理附件上传"""
    try:
        update_config_if_needed()
        
        # 验证文件是否存在
        if 'attachment' not in request.files:
            logger.warning("No file part in request")
            return jsonify({'success': False, 'message': 'No file part'})
        
        # 获取文件和用户信息
        file = request.files['attachment']
        original_filename = request.form.get('originalFilename')
        user = request.form.get('user')
        key = request.form.get('key')
        token = request.form.get('token')
        
        logger.info(f"Processing attachment upload for user: {user}, file: {original_filename}")
        
        # 验证用户信息
        if not user or not key:
            logger.warning("Missing user credentials")
            return jsonify({'success': False, 'message': 'Missing user or key'}), 400

        if not authenticate_user(user, key):
            logger.warning(f"Invalid credentials for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
        
        if not verify_access_token(user, token, user_key_config):
            logger.warning(f"Invalid token for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        # 验证文件名
        if file.filename == '':
            logger.warning("No selected file")
            return jsonify({'success': False, 'message': 'No selected file'})
        
        # 处理文件上传
        if file:
            # 检查上传限制
            if not check_and_update_upload_count(user, 'attachment') and user != "admin":
                logger.warning(f"Upload limit exceeded for user: {user}")
                return jsonify({'success': False, 'message': 'Daily upload limit exceeded'}), 429
            
            # 处理文件名和保存文件
            filename = secure_filename_with_chinese(original_filename)
            timestamp = int(time.time())
            base, extension = os.path.splitext(filename)
            filename = f"{base}_{timestamp}{extension}"
            file_path = os.path.join(ATTACHMENT_FOLDER, filename)
            
            file.save(file_path)
            logger.info(f"Successfully saved attachment: {filename} for user: {user}")
            
            # 创建响应
            response = jsonify({
                'success': True, 
                'filename': filename, 
                'originalFilename': original_filename
            })
            
            # 设置响应头
            response.headers.update({
                'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
                'Pragma': 'no-cache',
                'Expires': '0'
            })
            
            return response

        logger.error("File upload failed")
        return jsonify({'success': False, 'message': 'File upload failed'})
        
    except Exception as e:
        logger.error(f"Error in upload_attachment: {str(e)}")
        return jsonify({
            'success': False, 
            'message': 'Internal server error',
            'error': str(e)
        }), 500

@app.route('/api/download_attachment/<filename>')
def download_attachment(filename):
    try:
        update_config_if_needed()       
        # 获取请求参数
        user = request.args.get('user')
        key = request.args.get('key')
        token = request.args.get('token')
        
        # 记录请求信息（不包含敏感数据）
        logger.info(f"Download attachment request for file: {filename} by user: {user}")
        
        # 验证必需参数
        if not user or not key:
            logger.warning(f"Missing user or key for download request")
            return jsonify({
                'success': False,
                'message': 'Missing user or key'
            }), 400

        # 验证用户凭据
        if not authenticate_user(user, key):
            logger.warning(f"Invalid credentials for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid credentials'
            }), 401

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.warning(f"Invalid or expired token for user: {user}")
            return jsonify({
                'success': False,
                'message': 'Invalid or expired token. Please log in again.'
            }), 401

        # 检查文件是否存在
        file_path = os.path.join(ATTACHMENT_FOLDER, filename)
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return jsonify({
                'success': False,
                'message': 'File not found'
            }), 404
        
        # 发送文件
        logger.info(f"Sending file: {filename} to user: {user}")
        return send_file(
            file_path, 
            as_attachment=True, 
            download_name=filename,
            mimetype='application/octet-stream'
        )

    except Exception as e:
        logger.error(f"Error in download_attachment: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An error occurred while processing your request',
            'error': str(e)
        }), 500




#####################################################
#blog访问量api
#####################################################
def safe_load_json(file_path, default_value=None):
    """安全地加载JSON文件，如果文件损坏会尝试修复或返回默认值"""
    try:
        # 如果文件不存在，返回默认值
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}")
            return default_value if default_value is not None else {}

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            
        # 如果文件为空，返回默认值
        if not content:
            logger.warning(f"Empty file: {file_path}")
            return default_value if default_value is not None else {}

        try:
            # 尝试解析JSON
            return json.loads(content)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in {file_path}: {str(e)}")
            
            # 尝试修复常见的JSON格式问题
            try:
                # 移除可能的多余逗号
                content = re.sub(r',\s*}', '}', content)
                content = re.sub(r',\s*]', ']', content)
                
                # 确保以单个JSON对象结束
                content = re.sub(r'}\s*{', '},{', content)
                content = content.strip()
                if not content.startswith('{'):
                    content = '{' + content
                if not content.endswith('}'):
                    content = content + '}'
                    
                # 尝试再次解析
                fixed_data = json.loads(content)
                
                # 如果修复成功，保存修复后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(fixed_data, f, ensure_ascii=False, indent=4)
                
                logger.info(f"Successfully fixed and saved JSON file: {file_path}")
                return fixed_data
                
            except Exception as fix_error:
                logger.error(f"Could not fix JSON file {file_path}: {str(fix_error)}")
                return default_value if default_value is not None else {}
                
    except Exception as e:
        logger.error(f"Error loading file {file_path}: {str(e)}")
        return default_value if default_value is not None else {}
def load_views():
    """加载浏览量数据"""
    return safe_load_json(VIEWS_FILE, default_value={})

def save_views(views):
    """保存浏览量数据，确保JSON格式正确"""
    try:
        # 确保views是一个普通字典
        views_dict = dict(views)
        
        # 移除任何无效的键值对
        views_dict = {k: v for k, v in views_dict.items() if k and v is not None}
        
        # 按键排序并保存
        with open(VIEWS_FILE, 'w', encoding='utf-8') as f:
            json.dump(dict(sorted(views_dict.items())), f, indent=2, ensure_ascii=False)
            f.write('\n')  # 确保文件以换行符结束
            
    except Exception as e:
        logger.error(f"Error saving views: {str(e)}")
        raise

@app.route('/api/record_view', methods=['POST'])
def record_view():
    """记录博客浏览量"""
    try:
        data = request.json
        filename = data.get('filename')
        
        if not filename:
            logger.warning("Record view request missing filename")
            return jsonify({
                "success": False, 
                "message": "Missing filename"
            }), 400

        logger.info(f"Recording view for blog: {filename}")
        
        # 加载和更新浏览量
        views = load_views()
        if filename not in views:
            views[filename] = 0
        views[filename] += 1
        
        # 保存更新后的浏览量
        save_views(views)
        
        logger.info(f"Successfully recorded view for {filename}. Total views: {views[filename]}")
        
        # 创建响应对象并设置缓存控制
        response = jsonify({
            "success": True, 
            "views": views[filename]
        })
        
        response.headers.update({
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        })
        
        return response
        
    except Exception as e:
        logger.error(f"Error recording view: {str(e)}")
        return jsonify({
            "success": False, 
            "message": "Error recording view",
            "error": str(e)
        }), 500

@app.route('/api/get_views/<filename>', methods=['GET'])
def get_views(filename):
    views = load_views()
    return jsonify({"success": True, "views": views.get(filename, 0)})





@app.route('/api/get_blog_stats', methods=['GET'])
def get_blog_stats():
    try:
        # 更新配置
        update_config_if_needed()

        # 使用安全加载函数加载数据
        views = safe_load_json(VIEWS_FILE, {})

        # 获取所有博客文件名
        all_filenames = set(views.keys())

        # 从comments文件夹中获取所有评论文件，提取博客文件名
        if os.path.exists(COMMENTS_DIR):
            for comment_file in os.listdir(COMMENTS_DIR):
                if comment_file.endswith('_comments.json'):
                    # 从评论文件名中提取博客文件名
                    blog_filename = comment_file.replace('_comments.json', '.md')
                    all_filenames.add(blog_filename)

        # 构建统计数据
        stats = {}
        for filename in all_filenames:
            # 获取评论数量
            blog_comments = load_blog_comments(filename)
            comment_count = len(blog_comments)

            stats[filename] = {
                'views': views.get(filename, 0),
                'comments': comment_count
            }

        return jsonify({
            "success": True,
            "stats": stats
        })

    except Exception as e:
        logger.error(f"Error in get_blog_stats: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@app.route('/api/get_remaining_uploads/<user>')
def get_remaining_uploads(user):
    # URL 解码用户名
    try:
        # 尝试不同的编码方式进行解码
        try:
            # 首先尝试UTF-8解码
            decoded_user = urllib.parse.unquote(user, encoding='utf-8')
        except:
            try:
                # 如果UTF-8失败，尝试GBK解码（常用于中文）
                decoded_user = urllib.parse.unquote(user, encoding='gbk')
            except:
                # 最后尝试Latin-1（ISO-8859-1）解码，它可以处理任何8位字符
                decoded_user = urllib.parse.unquote(user, encoding='latin-1')
                # 然后转换为UTF-8
                if not isinstance(decoded_user, str):
                    decoded_user = decoded_user.decode('utf-8', errors='replace')
        
        logger.info(f"Getting remaining uploads for user: {decoded_user}")
        
    
        counts = load_user_upload_counts()
        user_counts = counts.get(decoded_user, {})
        
        # 检查用户的 VIP 状态
        is_vip = False
        if decoded_user in user_key_config['credentials']['usernames']:
            user_info = user_key_config['credentials']['usernames'][decoded_user]
            if 'vip_expiry' in user_info:
                expiry_timestamp = user_info['vip_expiry']
                if expiry_timestamp > time.time():  # VIP 未过期
                    is_vip = True

        # 根据用户身份调整上传限制
        adjusted_limits = {}
        for upload_type in DAILY_UPLOAD_LIMITS:
            if decoded_user == "admin":
                adjusted_limits[upload_type] = 100  # 管理员无限制
            elif is_vip:
                adjusted_limits[upload_type] = DAILY_UPLOAD_LIMITS[upload_type] + vip_add_blog_limit  # VIP 用户额外 10 次
            else:
                adjusted_limits[upload_type] = DAILY_UPLOAD_LIMITS[upload_type] + 1  # 普通用户原有限制

        # 计算剩余次数
        remaining = {
            'image': adjusted_limits['image'] - user_counts.get('image', 0),
            'video': adjusted_limits['video'] - user_counts.get('video', 0),
            'attachment': adjusted_limits['attachment'] - user_counts.get('attachment', 0),
            'is_vip': is_vip  # 添加 VIP 状态信息
        }
        logger.info(f"Remaining uploads for {decoded_user}: {remaining}")
        
        # 创建响应对象并设置禁用缓存的头部
        response = jsonify(remaining)
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        return response
    except Exception as e:
        logger.error(f"Error processing remaining uploads request: {str(e)}")
        return jsonify({"error": "Failed to process request"}), 500    
#####################################################
#评论api
#####################################################
# 修改加载评论数据的函数
def load_comments():
    """加载评论数据"""
    return safe_load_json(COMMENTS_FILE, default_value={})

def save_comments(comments):
    with open(COMMENTS_FILE, 'w', encoding='utf-8') as f:
        json.dump(comments, f, indent=2, ensure_ascii=False)

def get_comment_file_path(blog_filename):
    """根据博客文件名生成对应的评论文件路径"""
    # 移除.md扩展名，添加_comments.json后缀
    base_name = blog_filename.replace('.md', '')
    comment_filename = f"{base_name}_comments.json"
    return os.path.join(COMMENTS_DIR, comment_filename)

def load_blog_comments(blog_filename):
    """加载指定博客的评论数据"""
    comment_file_path = get_comment_file_path(blog_filename)
    return safe_load_json(comment_file_path, default_value=[])

def save_blog_comments(blog_filename, comments):
    """保存指定博客的评论数据"""
    comment_file_path = get_comment_file_path(blog_filename)
    with open(comment_file_path, 'w', encoding='utf-8') as f:
        json.dump(comments, f, indent=2, ensure_ascii=False)

@app.route('/api/submit_comment', methods=['POST'])
def submit_comment():
    update_config_if_needed()
    data = request.json
    user = data.get('user')
    key = data.get('key')
    filename = data.get('filename')
    comment_text = data.get('comment')
    token = data.get('token')
    if not all([user, key, filename, comment_text]):
        return jsonify({"success": False, "message": "Missing required data"}), 400

    # 验证用户身份
    if not authenticate_user(user, key):
        logger.warning(f"Authentication failed for user: {user}")
        return jsonify({"success": False, "message": "未授权的操作"}), 401

    if not verify_access_token(user, token, user_key_config):
        return jsonify({"success": False, "message": "Invalid or expired token"}), 401

    # 保存评论到单独的JSON文件
    blog_comments = load_blog_comments(filename)

    new_comment = {
        "id": str(uuid.uuid4()),  # 生成唯一的 ID
        "user": user,
        "comment": comment_text,
        "timestamp": datetime.now().isoformat()
    }
    blog_comments.append(new_comment)
    save_blog_comments(filename, blog_comments)

    return jsonify({"success": True, "message": "Comment submitted successfully"})

@app.route('/api/get_comments/<filename>', methods=['GET'])
def get_comments(filename):
    """获取指定博客的评论列表"""
    try:
        logger.info(f"Fetching comments for blog: {filename}")
        
        # 从单独的JSON文件加载评论数据
        file_comments = load_blog_comments(filename)
        
        # 按时间戳排序
        file_comments.sort(key=lambda x: x['timestamp'], reverse=True)
        
        logger.info(f"Found {len(file_comments)} comments for {filename}")
        
        # 创建响应对象
        response = jsonify({
            "success": True, 
            "comments": file_comments
        })
        
        # 设置禁用缓存的头部
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting comments for {filename}: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Failed to load comments"
        }), 500



@app.route('/api/delete_comment', methods=['POST'])
def delete_comment():
    update_config_if_needed()
    data = request.json
    user = data.get('user')
    key = data.get('key')
    blog_filename = data.get('blog_filename')
    comment_id = data.get('comment_id')
    token = data.get('token')
    logger.info(f"Attempting to delete comment. User: {user}, Blog: {blog_filename}, Comment ID: {comment_id}")
    
    # 验证用户身份
    if not authenticate_user(user, key):
        logger.warning(f"Authentication failed for user: {user}")
        return jsonify({"success": False, "message": "未授权的操作"}), 401

    if not verify_access_token(user, token, user_key_config):
        return jsonify({"success": False, "message": "Invalid or expired token"}), 401

    # 从单独的JSON文件读取评论
    blog_comments = load_blog_comments(blog_filename)
    logger.info(f"Loaded comments for blog {blog_filename}: {blog_comments}")

    # 检查是否有评论
    if not blog_comments:
        return jsonify({"success": False, "message": "找不到指定的博客评论"}), 404

    # 查找要删除的评论
    comment_to_delete = next((comment for comment in blog_comments if comment['id'] == comment_id), None)
    if not comment_to_delete:
        logger.warning(f"Comment not found: {comment_id}")
        return jsonify({"success": False, "message": "找不到指定的评论"}), 404

    # 检查用户是否有权限删除评论
    if user != 'admin' and user != comment_to_delete['user']:
        logger.warning(f"Permission denied. User: {user}, Comment author: {comment_to_delete['user']}")
        return jsonify({"success": False, "message": "没有权限删除此评论"}), 403

    # 删除指定的评论
    blog_comments = [comment for comment in blog_comments if comment['id'] != comment_id]
    logger.info(f"Comment deleted successfully")

    # 保存更新后的评论文件
    save_blog_comments(blog_filename, blog_comments)

    return jsonify({"success": True, "message": "评论已成功删除"})











#####################################################################################################

@app.route('/api/blog_list_root/')
def blog_list_root():
    user = request.args.get('user')
    key = request.args.get('key')
    token = request.args.get('token')
    update_config_if_needed()   
    if not user or not key or not token:
        abort(400, description="Missing user, key or token")

    if not authenticate_user(user, key):
        abort(401, description="Invalid user or key")
          
    # 验证token
    if not verify_access_token(user, token, user_key_config):
        abort(401, description="Invalid or expired token")
        
    if user != "admin":
        abort(402, description="非管理员无法打开")
        
    return send_from_directory('.', 'blog_list_root.html')


@app.route('/api/view_blog/')
def view_blog():
    """提供博客查看页面"""
    try:
        logger.info("Serving view_blog.html")
        response = send_from_directory('.', 'view_blog.html')
        
        # 设置缓存控制
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        return response
    except Exception as e:
        logger.error(f"Error serving view_blog.html: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/blog_list/')
def blog_list():
    """提供博客列表页面"""
    try:
        logger.info("Serving blog_list.html")
        response = send_from_directory('.', 'blog_list.html')
        
        # 设置缓存控制
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        return response
    except Exception as e:
        logger.error(f"Error serving blog_list.html: {str(e)}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/get_all_blogs')
def get_all_blogs():
    with open('user_blogs.json', 'r') as f:
        user_blogs = json.load(f)
    
    all_blogs = []
    for user, blogs in user_blogs.items():
        for blog in blogs:
            blog['user'] = user
            all_blogs.append(blog)
    
    # 按最后修改时间排序
    all_blogs.sort(key=lambda x: datetime.fromisoformat(x['last_modified']), reverse=True)
    
    # 获取博客内容和第一张图片
    for blog in all_blogs:
        file_path = os.path.join('blogs', blog['filename'])
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 获取标题
        title = content.split('\n')[0].lstrip('# ')
        blog['title'] = title
        
        # 查找第一张图片
        soup = BeautifulSoup(content, 'html.parser')
        img_tag = soup.find('img')
        if img_tag:
            blog['first_image'] = {
                'src': img_tag.get('src', ''),
                'alt': img_tag.get('alt', '博客图片'),
                'width': img_tag.get('width', '80')  # 默认宽度为800
            }
    
    return jsonify(all_blogs)





###############################
#上传图片
###############################
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
def secure_filename_with_timestamp(filename, username=None):
    # 获取安全的文件名
    secure_name = secure_filename(filename)
    # 分离文件名和扩展名
    name, ext = os.path.splitext(secure_name)
    # 添加时间戳
    timestamp = int(time.time())
    # 组合新的文件名，如果有用户名则添加用户名前缀
    if username:
        return f"{username}_{name}_{timestamp}{ext}"
    else:
        return f"{name}_{timestamp}{ext}"

@app.route('/api/uploadimage', methods=['POST'])
def upload_image():
    if 'image' not in request.files:
        return jsonify({'success': False, 'message': 'No file part'}), 400
    file = request.files['image']
    user = request.form.get('user')
    if not user:
        return jsonify({'success': False, 'message': 'User not specified'}), 400
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No selected file'}), 400
    
    # 检查文件类型是否允许
    if file and allowed_file(file.filename):
        # 检查上传次数限制（admin用户不受限制）
        if not check_and_update_upload_count(user, 'image') and user != "admin":
            return jsonify({'success': False, 'message': 'Daily upload limit exceeded'}), 429
        
        # 生成安全的文件名
        filename = secure_filename_with_timestamp(file.filename, user)  # 传递用户名参数
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        image_url = f'/api/uploads/{filename}'
        return jsonify({'success': True, 'imageUrl': image_url})
    return jsonify({'success': False, 'message': 'File type not allowed'}), 400


# 添加一个路由来提供上传的图片
@app.route('/api/uploads/<filename>')
def uploaded_file(filename):
    """提供上传的图片文件访问"""
    try:
        # 记录访问日志
        logger.info(f"Serving uploaded file: {filename}")
        
        # 检查文件是否存在
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {filename}")
            return jsonify({'error': 'File not found'}), 404
            
        # 设置缓存控制
        response = send_from_directory(UPLOAD_FOLDER, filename)
        response.headers['Cache-Control'] = 'public, max-age=31536000'  # 缓存一年
        
        # 记录成功响应
        logger.info(f"Successfully served file: {filename}")
        return response
        
    except Exception as e:
        logger.error(f"Error serving file {filename}: {str(e)}")
        return jsonify({'error': str(e)}), 500

#############################################################################################################
#博客
#############################################################################################################

def load_user_blogs():
    if os.path.exists(USER_BLOGS_FILE):
        try:
            with open(USER_BLOGS_FILE, 'r',encoding='utf-8') as f:
                content = f.read().strip()
                if content:  # 检查文件是否为空
                    return json.loads(content)
                else:
                    return {}  # 如果文件为空，返回空字典
        except json.JSONDecodeError:
            print(f"Error decoding JSON from {USER_BLOGS_FILE}. File might be corrupted.")
            return {}  # 如果 JSON 解码失败，返回空字典
    return {}  # 如果文件不存在，返回空字典

def save_user_blogs(user_blogs):
    with open(USER_BLOGS_FILE, 'w') as f:
        json.dump(user_blogs, f, indent=2)

if not os.path.exists(USER_BLOGS_FILE):
    save_user_blogs({})

@app.route('/blog/')
def index():
    update_config_if_needed()   
    user = request.args.get('user')
    key = request.args.get('key')
    token = request.args.get('token') 
    user_info = user_key_config['credentials']['usernames'][user]   
    if not user or not key:
        abort(401, description="Missing user or key")

    if not authenticate_user(user, key):
        abort(401, description="Invalid user or key")
    
    if not verify_access_token(user, token, user_key_config):
        print(f"user_info['token'] : {user_info['token'] }")
        print(f"token: {token}")
        print(f"user_info['expiry']: {user_info['expiry']}")
        abort(401, description="Invalid or expired token")  
        
    return send_from_directory('.', 'blog_page.html')



@app.route('/save_blog', methods=['POST'])
def save_blog():
    data = request.json
    markdown = data.get('markdown')
    user = data.get('user', 'anonymous')
    key = data.get('key')
    graph_data = data.get('graphData')
    category = data.get('category')  # 新增分类参数
    
    if not markdown or not category:  # 检查必需参数
        return jsonify({
            "success": False, 
            "message": "Missing required content or category"
        }), 400

    # 创建文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{user}_{timestamp}.md"
    
    # 确保目录存在
    os.makedirs('blogs', exist_ok=True)
    os.makedirs('graph', exist_ok=True)

    # 将 Markdown 内容写入文件
    blog_path = os.path.join('blogs', filename)
    with open(blog_path, 'w', encoding='utf-8') as f:
        f.write(markdown)

    # 将图谱数据保存为 JSON 文件
    graph_filename = f"{user}_{timestamp}.json"
    graph_path = os.path.join('graph', graph_filename)
    with open(graph_path, 'w', encoding='utf-8') as f:
        json.dump(graph_data, f, ensure_ascii=False, indent=2)

    # 更新用户博客记录
    user_blogs = load_user_blogs()
    if user not in user_blogs:
        user_blogs[user] = []
    user_blogs[user].append({
        'filename': filename,
        'graph_filename': graph_filename,
        'category': category,  # 添加分类信息
        'created_at': datetime.now().isoformat(),
        'last_modified': datetime.now().isoformat()
    })
    save_user_blogs(user_blogs)

    return jsonify({
        "success": True, 
        "message": "Blog post and graph published successfully",
        "filename": filename,
        "graph_filename": graph_filename
    })




# 添加新的路由来获取博客分类
@app.route('/api/blog/categories')
def get_blog_categories():
    try:
        # 获取用户参数
        user = request.args.get('user')
        stu_class = authenticate_user_class(user)
        # 记录用户请求信息
        if user:
            logger.info(f"User {user} requested blog categories")
        
        with open('blog_categories.json', 'r', encoding='utf-8') as f:
            categories = json.load(f)
        
        # 如果没有获取到学生班级信息，过滤掉以"作业"结尾的分类
        if not stu_class:
            filtered_categories = [category for category in categories['categories'] if not category.endswith('作业')]
            logger.info(f"filtered_categories {filtered_categories}  blog categories")
            return jsonify({
                "success": True,
                "categories": filtered_categories
            })
        else:
            # 有学生班级信息，返回所有分类
            return jsonify({
                "success": True,
                "categories": categories['categories']
            })
    except Exception as e:
        logger.error(f"Error loading blog categories: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Failed to load categories",
            "categories": ["其他"]  # 返回默认分类
        })



#############################################################################################################
#
#############################################################################################################







#保存草稿
@app.route('/api/save_draft', methods=['POST'])
def save_draft():
    data = request.json
    user = data.get('user')
    title = data.get('title')
    content = data.get('content')
    
    if not all([user, title, content]):
        return jsonify({"success": False, "message": "Missing required data"}), 400

    # 创建草稿文件名
    draft_filename = f"{user}_draft.md"
    
    # 确保 'drafts' 目录存在
    os.makedirs('drafts', exist_ok=True)

    # 将草稿内容写入文件
    draft_path = os.path.join('drafts', draft_filename)
    with open(draft_path, 'w', encoding='utf-8') as f:
        f.write(f"# {title}\n\n{content}")

    return jsonify({
        "success": True, 
        "message": "Draft saved successfully",
        "filename": draft_filename
    })
#获取草稿

@app.route('/api/get_draft/<user>', methods=['GET'])
def get_draft(user):
    draft_filename = f"{user}_draft.md"
    draft_path = os.path.join('drafts', draft_filename)
    
    if os.path.exists(draft_path):
        with open(draft_path, 'r', encoding='utf-8') as f:
            content = f.read()
        lines = content.split('\n')
        title = lines[0].replace('# ', '')
        body = '\n'.join(lines[2:])
        return jsonify({
            "success": True,
            "title": title,
            "content": body
        })
    else:
        # 返回 200 状态码，并提供友好的消息
        return jsonify({
            "success": False,
            "message": "No draft found",
            "title": "",
            "content": ""
        }), 200
#删除草稿
@app.route('/api/delete_draft/<user>', methods=['POST'])
def delete_draft(user):
    draft_filename = f"{user}_draft.md"
    draft_path = os.path.join('drafts', draft_filename)
    
    logger.debug(f"Attempting to delete draft for user: {user}")
    logger.debug(f"Draft path: {draft_path}")
    
    if os.path.exists(draft_path):
        try:
            os.remove(draft_path)
            logger.info(f"Draft deleted successfully for user: {user}")
            return jsonify({"success": True, "message": "Draft deleted successfully"})
        except Exception as e:
            logger.error(f"Error deleting draft for user {user}: {str(e)}")
            return jsonify({"success": False, "message": f"Error deleting draft: {str(e)}"}), 500
    else:
        logger.info(f"No draft found to delete for user: {user}")
        return jsonify({"success": True, "message": "No draft to delete"})
    



   
@app.route('/api/delete_blog', methods=['POST'])
def delete_blog():
    update_config_if_needed()
    data = request.json
    user = data.get('user')
    blog_user = data.get('blog_user')
    key = data.get('key')
    filename = data.get('filename')
    token = data.get('token')
    if not all([user, filename]):
        return jsonify({"success": False, "message": "Missing required data"}), 400


    if not authenticate_user(user, key):
        return jsonify({'success': False, 'message': 'Invalid user or key'}), 401

    if not verify_access_token(user, token, user_key_config):
        return jsonify({'success': False, 'message': 'Invalid or expired token'}), 401

    user_blogs = load_user_blogs()
    if user not in user_blogs:
        return jsonify({"success": False, "message": "User not found"}), 404

    blog_entry = next((blog for blog in user_blogs[blog_user] if blog['filename'] == filename), None)
    if not blog_entry:
        return jsonify({"success": False, "message": "Blog not found"}), 404

    # 删除博客文件
    blog_file_path = os.path.join('blogs', filename)
    if os.path.exists(blog_file_path):
        os.remove(blog_file_path)

    # 删除对应的图谱 JSON 文件
    graph_filename = os.path.splitext(filename)[0] + '.json'
    graph_file_path = os.path.join('graph', graph_filename)
    if os.path.exists(graph_file_path):
        os.remove(graph_file_path)

    # 从用户博客列表中移除
    user_blogs[blog_user] = [blog for blog in user_blogs[blog_user] if blog['filename'] != filename]
    save_user_blogs(user_blogs)
    # 删除相关的评论
    comments = load_comments()
    if filename in comments:
        del comments[filename]
        save_comments(comments)

    # 删除homework_score.json中的对应分数记录
    try:
        # 提取第一个下划线前的内容作为学生姓名
        student_name = filename.split('_')[0]
        
        # 获取学生班级
        stu_class = authenticate_user_class(student_name)
        
        # 如果找到班级信息，删除对应的分数记录
        if stu_class:
            scores_file = 'homework_score.json'
            if os.path.exists(scores_file):
                with open(scores_file, 'r', encoding='utf-8') as f:
                    homework_scores = json.load(f)
                
                # 检查并删除分数记录
                if (stu_class in homework_scores and 
                    student_name in homework_scores[stu_class] and 
                    filename in homework_scores[stu_class][student_name]):
                    # 删除文件对应的分数
                    del homework_scores[stu_class][student_name][filename]
                    
                    # 如果学生没有其他分数记录，也删除学生记录
                    if not homework_scores[stu_class][student_name]:
                        del homework_scores[stu_class][student_name]
                        
                    # 如果班级没有其他学生记录，也删除班级记录
                    if not homework_scores[stu_class]:
                        del homework_scores[stu_class]
                    
                    # 保存更新后的分数记录
                    with open(scores_file, 'w', encoding='utf-8') as f:
                        json.dump(homework_scores, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"已删除作业分数记录：班级={stu_class}, 学生={student_name}, 文件={filename}")
    except Exception as e:
        logger.error(f"删除作业分数记录时出错: {e}")
        # 继续执行，不因为删除分数记录失败而中断博客删除操作

    return jsonify({
        "success": True, 
        "message": "Blog and associated graph file deleted successfully"
    })










@app.route('/api/get_user_blogs/<user>', methods=['GET'])
def get_user_blogs(user):
    try:
        # 加载用户博客数据
        user_blogs = load_user_blogs()       
        if user in user_blogs:
            blogs_with_titles = []
            
            # 处理每个博客条目
            for blog in user_blogs[user]:
                try:
                    filename = blog['filename']
                    file_path = os.path.join('blogs', filename)                    
                    # 检查文件是否存在
                    if os.path.exists(file_path):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            first_line = f.readline().strip()
                            title = first_line[2:] if first_line.startswith('# ') else first_line
                    else:
                        title = "无标题"
                    
                    # 创建博客信息对象，保留所有原始字段
                    blog_info = {
                        'title': title,
                        'category': blog.get('category', '未分类'),  # 确保包含 category 字段
                        **blog  # 保留所有原始字段
                    }
                    
                    blogs_with_titles.append(blog_info)
                    
                except Exception as e:
                    print(f"Error processing blog entry: {str(e)}")
                    continue
            
            # 按创建时间倒序排序
            blogs_with_titles.sort(key=lambda x: x['created_at'], reverse=True)
            
            response = jsonify({
                "success": True,
                "blogs": blogs_with_titles
            })
            
            # 添加禁用缓存的头部
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            
            return response
        else:
            return jsonify({
                "success": False, 
                "message": "User not found",
                "blogs": []
            })
            
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Server error: {str(e)}",
            "blogs": []
        })

# 新增：获取用户的所有博客
@app.route('/api/get_blog_content/<filename>', methods=['GET'])
def get_blog_content(filename):
    """获取博客内容和分类信息"""
    try:
        logger.info(f"Fetching blog content: {filename}")
        
        # 读取用户博客数据以获取分类信息
        with open('user_blogs.json', 'r') as f:
            user_blogs = json.load(f)

        # 查找对应的博客信息
        blog_data = None
        for user, blogs in user_blogs.items():
            for blog in blogs:
                if blog['filename'] == filename:
                    blog_data = blog
                    blog_data['user'] = user  # 添加用户信息
                    break
            if blog_data:
                break

        # 如果没有找到博客元数据，返回404
        if not blog_data:
            logger.warning(f"Blog metadata not found for: {filename}")
            return jsonify({"success": False, "message": "Blog metadata not found"}), 404

        # 读取博客内容
        file_path = os.path.join('blogs', filename)
        if not os.path.exists(file_path):
            logger.warning(f"Blog not found: {filename}")
            return jsonify({"success": False, "message": "Blog not found"}), 404
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        logger.info(f"Successfully retrieved blog: {filename}")
        
        # 创建响应对象，包含内容和分类信息
        response = make_response(jsonify({
            "success": True, 
            "content": content,
            "category": blog_data.get('category', '未分类')  # 返回分类信息
        }))
        
        # 设置缓存控制
        response.headers.update({
            'Cache-Control': 'public, max-age=30',
            'ETag': hashlib.md5(content.encode()).hexdigest()
        })
        
        return response
        
    except Exception as e:
        logger.error(f"Error fetching blog {filename}: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error reading blog: {str(e)}"
        }), 500

# 新增：更新博客内容
@app.route('/api/update_blog', methods=['POST'])
def update_blog():
    data = request.json
    user = data.get('user')
    filename = data.get('filename')
    new_content = data.get('content')
    graph_data = data.get('graphData')
    category = data.get('category')  # 获取分类信息
    logger.debug(f"Received update request for user: {user}, filename: {filename}")
    logger.debug(f"Graph data type: {type(graph_data)}")
    logger.debug(f"Graph data keys: {graph_data.keys() if isinstance(graph_data, dict) else 'Not a dict'}")
    logger.debug(f"Graph data content: {json.dumps(graph_data, indent=2)}")

    if not all([user, filename, new_content]):
        return jsonify({"success": False, "message": "Missing required data"}), 400

    user_blogs = load_user_blogs()
    if user not in user_blogs:
        return jsonify({"success": False, "message": "User not found"}), 404

    blog_entry = next((blog for blog in user_blogs[user] if blog['filename'] == filename), None)
    if not blog_entry:
        return jsonify({"success": False, "message": "Blog not found"}), 404

    # 更新博客文件内容
    blog_file_path = os.path.join('blogs', filename)
    with open(blog_file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    logger.debug(f"Blog content updated: {blog_file_path}")
    # 更新分类信息
    blog_entry['category'] = category
    logger.debug(f"Category updated to: {category}")
    # 只在 graph_data 不为空且有效时更新知识图谱 JSON 文件
    if graph_data and isinstance(graph_data, dict) and 'nodes' in graph_data and graph_data['nodes']:
        graph_filename = filename.split('.')[0] + '.json'
        graph_path = os.path.join('graph', graph_filename)
        logger.debug(f"Attempting to update graph file: {graph_path}")
        try:
            # 确保 graph 目录存在
            os.makedirs(os.path.dirname(graph_path), exist_ok=True)
            
            with open(graph_path, 'w', encoding='utf-8') as f:
                json.dump(graph_data, f, ensure_ascii=False, indent=2)
            logger.debug(f"Graph file updated successfully: {graph_path}")
        except Exception as e:
            logger.error(f"Error updating graph file: {str(e)}")
            return jsonify({"success": False, "message": f"Error updating graph: {str(e)}"}), 500
    else:
        logger.warning("No valid graph data received, skipping graph update")
        logger.warning(f"Received graph_data: {graph_data}")

    # 更新最后修改时间
    blog_entry['last_modified'] = datetime.now().isoformat()
    save_user_blogs(user_blogs)

    return jsonify({"success": True, "message": "Blog, category and graph updated successfully"})




# 确保已安装 FFmpeg，并且可以在命令行中访问
def secure_filename_with_extension(filename):
    # 分离文件名和扩展名
    name, ext = os.path.splitext(filename)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # 对文件名部分进行安全处理
    secure_name = secure_filename(name)
    
    # 如果安全处理后的文件名为空，使用一个默认名称
    if not secure_name:
        secure_name = "file"
        return f"{secure_name}_{timestamp}{ext}"
    # 添加当前时间作为唯一标识符
    
    
    # 重新组合文件名、时间戳和扩展名
    return f"{secure_name}{ext}"
def check_video_codec(file_path):
    command = [
        'ffprobe',
        '-v', 'error',
        '-select_streams', 'v:0',
        '-show_entries', 'stream=codec_name',
        '-of', 'default=noprint_wrappers=1:nokey=1',
        file_path
    ]
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        codec = result.stdout.strip()
        return codec.lower() in ['hevc', 'h265']
    except subprocess.CalledProcessError as e:
        logger.error(f"Error checking video codec: {e.stderr}")
        return False
def transcode_video(input_path, output_path):
    command = [
        'ffmpeg',
        '-i', input_path,
        '-c:v', 'libx264',
        '-crf', '23',
        '-preset', 'medium',
        '-c:a', 'aac',
        '-b:a', '192k',
        output_path
    ]
    try:
        subprocess.run(command, check=True, capture_output=True)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Transcoding failed: {e.stderr.decode()}")
        return False
    


# 视频上传
@app.route('/upload', methods=['POST'])
def upload_file():
    logger.info("Received upload request")
    logger.debug(f"Request headers: {request.headers}")
    logger.debug(f"Request files: {request.files}")
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    if file:
        filename = secure_filename_with_extension(file.filename)
        logger.debug(f"Secured filename: {filename}")
        file_path = os.path.join(VIDEO_DIR, filename)
        
        if os.path.exists(file_path):
            return jsonify({'error': 'File already exists', 'filename': filename}), 409  # 409 Conflict
        
        # 保存原始文件
        file.save(file_path)
        
        if not os.path.exists(file_path):
            return jsonify({'error': 'File upload failed'}), 500
        
        # 检查是否需要转码
        if check_video_codec(file_path):
            logger.info(f"Video {filename} is HEVC encoded. Transcoding...")
            transcoded_file_path = os.path.join(VIDEO_DIR, f"transcoded_{filename}")
            
            # 转码视频
            if transcode_video(file_path, transcoded_file_path):
                original_size = os.path.getsize(file_path)
                transcoded_size = os.path.getsize(transcoded_file_path)
                
                # 删除原始文件，使用转码后的文件
                os.remove(file_path)
                os.rename(transcoded_file_path, file_path)
                
                return jsonify({
                    'message': 'File uploaded and transcoded successfully',
                    'original_size': original_size,
                    'transcoded_size': transcoded_size,
                    'filename': filename
                }), 200
            else:
                # 如果转码失败，保留原始文件
                os.remove(transcoded_file_path)
                return jsonify({'error': 'Video transcoding failed, original file retained'}), 500
        else:
            logger.info(f"Video {filename} does not need transcoding.")
            file_size = os.path.getsize(file_path)
            return jsonify({
                'message': 'File uploaded successfully (no transcoding needed)',
                'size': file_size,
                'filename': filename
            }), 200




@app.route('/video/<path:filepath>')
def stream_video(filepath):
    """流式传输视频文件"""
    try:
        # 获取并验证用户认证参数
        user = request.args.get('user')
        key = request.args.get('key')
        token = request.args.get('token')
        
        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Invalid user or key: {user}")
            abort(401, description="Invalid user or key")

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Invalid token for user: {user}")
            abort(401, description="Invalid token")

        # 加载用户配置，检查是否有class属性
        user_class=authenticate_user_class(user)
        logger.info(f"用户班级userclass is {user_class}")
        # 如果用户有class属性，记录视频观看数据
        if user_class:
            # 提取文件名（不包含路径）
            video_filename = os.path.basename(filepath)
            # 记录视频观看数据
            record_video_viewing(user, user_class, video_filename)




        video_path = None
        
        # 检查路径是否包含文件夹
        if '/' in filepath:
            # 如果包含文件夹，从 COURSE_VIDEO_BASE 获取视频
            video_path = os.path.join(COURSE_VIDEO_BASE, filepath)
        else:
            # 如果只有文件名，直接从 VIDEO_DIR 获取视频
            video_path = os.path.join(VIDEO_DIR, filepath)

        # 检查文件是否存在
        if not video_path or not os.path.exists(video_path):
            logger.error(f"Video not found: {filepath}")
            abort(404, description="Video not found")
        
        # 获取文件大小
        size = os.path.getsize(video_path)
        
        # 处理范围请求
        range_header = request.headers.get('Range', None)
        if not range_header:
            return send_file(video_path)

        try:
            # 解析范围请求
            byte1, byte2 = 0, None
            match = re.search(r'bytes=(\d+)-(\d*)', range_header)
            if not match:
                return send_file(video_path)
                
            groups = match.groups()
            byte1 = int(groups[0])
            if groups[1]:
                byte2 = int(groups[1])

            # 计算响应长度
            if byte2 is not None:
                length = byte2 - byte1 + 1
            else:
                length = size - byte1

            # 设置缓冲区大小（例如8KB）
            buffer_size = 8192
            
            def generate():
                with open(video_path, 'rb') as f:
                    f.seek(byte1)
                    remaining = length
                    while remaining:
                        chunk_size = min(buffer_size, remaining)
                        data = f.read(chunk_size)
                        if not data:
                            break
                        remaining -= len(data)
                        yield data

            # 创建流式响应
            rv = Response(
                generate(),
                206,
                mimetype='video/mp4',
                content_type='video/mp4',
                direct_passthrough=True
            )
            
            # 设置响应头
            rv.headers.update({
                'Content-Range': f'bytes {byte1}-{byte1 + length - 1}/{size}',
                'Accept-Ranges': 'bytes',
                'Content-Length': str(length),
                'Cache-Control': 'public, max-age=86400'  # 缓存一天
            })

            return rv
            
        except (TypeError, ValueError) as e:
            logger.error(f"Error processing video request: {str(e)}")
            abort(400, description="Invalid range header")
            
    except IOError as e:
        logger.error(f"Error reading video file: {str(e)}")
        abort(500, description="Error reading video file")




@app.route('/subtitles/<path:filepath>')
def get_subtitles(filepath):
    """获取字幕文件"""
    try:
        subtitle_path = None
        base_name = os.path.splitext(os.path.basename(filepath))[0]
        
        # 检查路径是否包含文件夹
        if '/' in filepath:
            # 如果包含文件夹，从 COURSE_VIDEO_BASE 获取字幕
            folder = os.path.dirname(filepath)
            subtitle_path = os.path.join(COURSE_VIDEO_BASE, folder, f"{base_name}.json")
        else:
            # 如果只有文件名，直接从 VIDEO_DIR 获取字幕
            subtitle_path = os.path.join(VIDEO_DIR, f"{base_name}.json")
        
        if not subtitle_path or not os.path.exists(subtitle_path):
            logger.error(f"Subtitles not found: {filepath}")
            abort(404, description="Subtitles not found")
            
        return send_file(subtitle_path)
        
    except Exception as e:
        logger.error(f"Error serving subtitles: {str(e)}")
        abort(500, description="Error serving subtitles")



# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # 
# 网页视频api

@app.route('/api/init_video_chat', methods=['POST'])
def init_video_chat():
    try:
        data = request.json
        filename = data.get('filename', '').strip()
        folder = data.get('folder', '').strip()  # 确保去除空白字符
        user = data.get('user', '').strip()
        key = data.get('key', '').strip()
        token = data.get('token', '').strip()
        
        # 增加参数验证
        if not all([filename, user, key, token]):
            missing = []
            if not filename: missing.append('filename')
            if not user: missing.append('user')
            if not key: missing.append('key')
            if not token: missing.append('token')
            return jsonify({'success': False, 'message': f'Missing required parameters: {", ".join(missing)}'}), 400
        
        # 清理文件名，移除任何可能导致路径问题的字符
        filename = os.path.basename(filename)
        if folder:
            folder = os.path.normpath(folder)
        
        logger.info(f"Sanitized params - filename: {filename}, folder: {folder}, user: {user}")

        # 验证用户
        if not authenticate_user(user, key):
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            return jsonify({'success': False, 'message': 'Invalid token'}), 401

        if_vip_user = is_user_vip(user)
        # 获取字幕文件路径时增加错误处理
        try:
            subtitle_name = os.path.splitext(filename)[0] + ".json"
            subtitle_path = os.path.join(COURSE_VIDEO_BASE, folder, subtitle_name) if folder else os.path.join(COURSE_VIDEO_BASE, subtitle_name)
            subtitle_path = os.path.normpath(subtitle_path)
            
            logger.info(f"Constructed subtitle_path: {subtitle_path}")
            
            if not os.path.isabs(subtitle_path):
                logger.error(f"Invalid path construction: Path is not absolute")
                return jsonify({'success': False, 'message': 'Invalid path construction'}), 500
                
        except Exception as e:
            logger.error(f"Path construction error: {str(e)}")
            return jsonify({'success': False, 'message': 'Error in path construction'}), 500
            
        # 从dataset_info.json获取对应folder的dataset_id
        dataset_id = "a3870650ffa111ef92aa2a5c03e306d6"  # 默认值test知识库
        try:
            dataset_info_path = os.path.join(COURSE_VIDEO_BASE, "dataset_info.json")
            if os.path.exists(dataset_info_path):
                with open(dataset_info_path, 'r', encoding='utf-8') as f:
                    dataset_info = json.load(f)
                
                # 如果folder存在于dataset_info中，获取对应的id
                if folder in dataset_info:
                    dataset_id = dataset_info[folder]["id"]
                    logger.info(f"Found dataset_id for folder '{folder}': {dataset_id}")
                else:
                    logger.warning(f"Folder '{folder}' not found in dataset_info.json, using default dataset_id")
        except Exception as e:
            logger.error(f"Error loading dataset_info.json: {str(e)}")
            # 继续使用默认dataset_id



        # 检查字幕文件并获取 doc_id
        if os.path.exists(subtitle_path):
            doc_id = uploadvideo_or_get_doc_id(subtitle_path, folder, api_key_model, parser_id="naive")
            logger.info(f"获得视频字幕doc_id: {doc_id}")
            if doc_id:
                return jsonify({
                    'success': True,
                    'doc_id': doc_id,
                    'dataset_id': dataset_id,
                    'if_vip_user': if_vip_user
                })
            else:
                return jsonify({'success': False, 'message': '字幕文件正在处理中，请稍后再试'}), 202
        else:
            return jsonify({'success': False, 'message': '字幕文件不存在'}), 404

    except Exception as e:
        logger.error(f"Error in init_video_chat: {str(e)}\nStack trace: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': '初始化失败，请稍后重试'}), 500



@app.route('/api/player/')
def video_player():
    """提供视频播放页面"""
    update_config_if_needed()
    try:
        # 从查询参数中获取文件名
        filename = request.args.get('filename')
        # 从查询参数中获取文件名、用户信息和令牌
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')
        if not filename:
            logger.error("No filename provided in query parameters")
            return jsonify({'error': 'No filename provided'}), 400
        # 验证用户
        if not authenticate_user(user, key):
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            return jsonify({'success': False, 'message': 'Invalid token'}), 401

        # URL 解码文件名
        filename = unquote(filename)
        
        logger.info(f"Serving video player page for: {filename}")
        response = send_from_directory('coursevideohtml', 'video_player.html')
        
        # 设置缓存控制
        response.headers.update({
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        })
        
        return response
    except Exception as e:
        logger.error(f"Error serving video player page: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/videos_page')
def video_list_page():
    """提供视频列表页面"""
    try:
        logger.info("Serving video list page")
        response = send_from_directory('coursevideohtml', 'video_list.html')
        
        # 设置缓存控制
        response.headers.update({
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        })
        
        return response
    except Exception as e:
        logger.error(f"Error serving video list page: {str(e)}")
        return jsonify({'error': str(e)}), 500

# 按文件名中第一个-前的数字排序
def get_lecture_number(filename):
    try:
        # 获取第一个-之前的部分并转换为数字
        number = float(filename.split('-')[0].strip())
        return number
    except (ValueError, IndexError):
        # 如果转换失败，返回无穷大，将该文件排在最后
        return float('inf')

@app.route('/api/videos')
def get_videos():
    """获取MIT物理课程视频列表API"""
    try:
        videos = []
        folder = request.args.get('folder', '')  # 获取特定文件夹的视频
        
        if folder:
            folder_path = os.path.join(COURSE_VIDEO_BASE, folder)
            if not os.path.exists(folder_path):
                return jsonify({'error': 'Folder not found'}), 404
            
            # 获取特定文件夹中的视频
            for filename in os.listdir(folder_path):
                if filename.lower().endswith(('.mp4', '.mkv', '.avi', '.mov')):
                    file_path = os.path.join(folder_path, filename)
                    try:
                        size = os.path.getsize(file_path)
                        videos.append({
                            'name': filename,
                            'size': size,
                            'folder': folder,
                            'path': file_path
                        })
                    except OSError as e:
                        logger.error(f"Error getting size for {filename}: {str(e)}")
                        continue

        # 按文件名排序
        videos.sort(key=lambda x: get_lecture_number(x['name']))
        
        response = jsonify(videos)
        response.headers.update({
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        })
        
        logger.info(f"Found {len(videos)} videos in MIT Physics directory")
        return response
        
    except Exception as e:
        logger.error(f"Error getting video list: {str(e)}")
        return jsonify({'error': str(e)}), 500




@app.route('/api/thumbnail/<path:filepath>')
def get_thumbnail(filepath):
    """获取视频缩略图"""
    try:
        # 解析文件夹和文件名
        folder, filename = os.path.split(filepath)
        video_path = os.path.join(COURSE_VIDEO_BASE, folder, filename)
        
        if not os.path.exists(video_path):
            return jsonify({'error': 'Video file not found'}), 404

        # 缩略图缓存目录
        cache_dir = os.path.join(os.path.dirname(video_path), '.thumbnails')
        os.makedirs(cache_dir, exist_ok=True)
        
        thumbnail_path = os.path.join(cache_dir, f"{os.path.splitext(filename)[0]}.jpg")
        
        # 检查缓存的缩略图是否存在
        if os.path.exists(thumbnail_path):
            with open(thumbnail_path, 'rb') as f:
                thumbnail_data = f.read()
        else:
            # 生成缩略图
            cap = cv2.VideoCapture(video_path)
            
            # 跳转到视频的1/3处获取帧
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.set(cv2.CAP_PROP_POS_FRAMES, total_frames // 3)
            
            ret, frame = cap.read()
            cap.release()
            
            if not ret:
                logger.error(f"Failed to extract frame from video: {filename}")
                return jsonify({'error': 'Failed to generate thumbnail'}), 500
            
            # 调整图像大小
            height, width = frame.shape[:2]
            target_width = 300
            target_height = int(height * (target_width / width))
            thumbnail = cv2.resize(frame, (target_width, target_height))
            
            # 转换为RGB并保存
            thumbnail_rgb = cv2.cvtColor(thumbnail, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(thumbnail_rgb)
            img.save(thumbnail_path, 'JPEG', quality=85)
            
            with open(thumbnail_path, 'rb') as f:
                thumbnail_data = f.read()

        response = make_response(thumbnail_data)
        response.headers.set('Content-Type', 'image/jpeg')
        response.headers.update({
            'Cache-Control': 'public, max-age=31536000',  # 缓存一年
        })
        return response
        
    except Exception as e:
        logger.error(f"Error generating thumbnail: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/thumbnails/<path:filepath>')
def serve_thumbnail(filepath):
    """提供缩略图文件"""
    try:
        # 构建完整的缩略图路径
        folder, filename = os.path.split(filepath)
        thumbnail_path = os.path.join(COURSE_VIDEO_BASE, folder, '.thumbnails', filename)
        return send_file(thumbnail_path)
    except Exception as e:
        logger.error(f"Error serving thumbnail: {str(e)}")
        return jsonify({'error': str(e)}), 500




# 添加静态文件路由
@app.route('/api/static/<path:filename>')
def serve_static(filename):
    """提供静态文件"""
    try:
        return send_from_directory('static', filename)
    except Exception as e:
        logger.error(f"Error serving static file: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/video_collections')
def get_video_collections():
    """获取视频合集信息"""
    try:
        collections = []
        base_path = os.path.join(main_dir, 'coursevideo')

        # 支持的视频文件扩展名
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'}

        # 遍历coursevideo目录下的所有文件夹
        for folder in os.listdir(base_path):
            folder_path = os.path.join(base_path, folder)
            if os.path.isdir(folder_path):
                # 统计视频文件数量
                video_count = 0
                try:
                    for file in os.listdir(folder_path):
                        file_path = os.path.join(folder_path, file)
                        if os.path.isfile(file_path):
                            # 检查文件扩展名
                            _, ext = os.path.splitext(file.lower())
                            if ext in video_extensions:
                                video_count += 1
                except Exception as e:
                    logger.warning(f"Error counting videos in {folder}: {str(e)}")
                    video_count = 0

                # 查找缩略图
                thumbnail_path = os.path.join(folder_path, '.thumbnails')
                thumbnail_url = None

                if os.path.exists(thumbnail_path):
                    # 获取第一个缩略图文件
                    thumbnail_files = [f for f in os.listdir(thumbnail_path)
                                    if f.endswith(('.jpg', '.png', '.jpeg'))]
                    if thumbnail_files:
                        thumbnail_url = f'/api/thumbnails/{folder}/{thumbnail_files[0]}'

                # 获取文件夹创建时间或修改时间作为排序依据
                try:
                    folder_stat = os.stat(folder_path)
                    created_time = folder_stat.st_ctime
                    modified_time = folder_stat.st_mtime
                except Exception as e:
                    logger.warning(f"Error getting folder stats for {folder}: {str(e)}")
                    created_time = 0
                    modified_time = 0

                collections.append({
                    'name': folder,
                    'thumbnail': thumbnail_url or '/api/static/default-thumbnail.jpg',
                    'video_count': video_count,
                    'created_time': created_time,
                    'modified_time': modified_time,
                    'display_name': folder.split('-')[1].strip() if '-' in folder else folder
                })

        # 按修改时间降序排序（最新的在前面）
        collections.sort(key=lambda x: x['modified_time'], reverse=True)

        logger.info(f"Found {len(collections)} collections with video counts")
        return jsonify({
            'success': True,
            'collections': collections,
            'total_collections': len(collections),
            'total_videos': sum(c['video_count'] for c in collections)
        })
    except Exception as e:
        logger.error(f"Error getting video collections: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/collections_page')
@require_auth
def video_collections_page():
    """提供视频合集页面"""
    try:
        user = request.args.get('user', '')
        logger.info(f"Collections page request - User: {user}")
        logger.info("Serving video collections page")
        
        response = send_from_directory('coursevideohtml', 'video_collections.html')

        # 设置缓存控制
        response.headers.update({
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        })

        return response
    except Exception as e:
        logger.error(f"Error serving video collections page: {str(e)}")
        return jsonify({'error': str(e)}), 500

#######################################################################################
#blog作业 打分

@app.route('/api/submit_score', methods=['POST'])
def submit_score():
    try:
        filename = request.args.get('filename')
        score = request.args.get('score')
        user = request.args.get('user')
        
        if not filename or not score:
            return jsonify({"success": False, "error": "缺少必要参数: 文件名或分数"}), 400
        
        decoded_filename = urllib.parse.unquote(filename)
        logger.info(f"解码后的文件名: {decoded_filename}")
        
        # 提取第一个下划线前的内容作为学生姓名
        student_name = decoded_filename.split('_')[0]
        logger.info(f"从文件名提取的学生姓名: {student_name}")
        
        # 获取学生班级
        stu_class = authenticate_user_class(student_name)
        logger.info(f"学生班级: {stu_class}, 学生姓名: {student_name}, 分数: {score}")
        
        # 如果找不到班级，使用默认班级
        if not stu_class:
            stu_class = "未分类班级"
            logger.warning(f"无法确定学生 {student_name} 所在班级，使用默认班级: {stu_class}")
        
        # 确保分数在有效范围内
        try:
            score = int(score)
            if score < 0 or score > 100:
                return jsonify({"success": False, "error": "分数必须在0到100之间"}), 400
        except ValueError:
            return jsonify({"success": False, "error": "分数必须是整数"}), 400

        # 读取现有分数 - 处理文件可能为空或格式错误的情况
        scores_file = 'homework_score.json'
        homework_scores = {}
        
        if os.path.exists(scores_file):
            try:
                with open(scores_file, 'r', encoding='utf-8') as f:
                    file_content = f.read().strip()
                    if file_content:  # 确保文件不为空
                        homework_scores = json.loads(file_content)
            except json.JSONDecodeError as e:
                logger.error(f"分数文件格式错误: {e}")
                # 如果文件格式错误，创建一个备份
                if os.path.getsize(scores_file) > 0:  # 只有当文件有内容时才备份
                    backup_file = f"{scores_file}.bak.{int(time.time())}"
                    try:
                        shutil.copy2(scores_file, backup_file)
                        logger.info(f"已创建损坏文件的备份: {backup_file}")
                    except Exception as be:
                        logger.error(f"创建备份文件失败: {be}")
                # 使用空字典继续
                homework_scores = {}
        
        # 确保班级、用户和文件的嵌套结构存在 - 修复了逻辑错误
        if stu_class not in homework_scores:  # 修正：检查stu_class而不是student_name
            homework_scores[stu_class] = {}
        
        if student_name not in homework_scores[stu_class]:
            homework_scores[stu_class][student_name] = {}
        
        # 更新特定用户的特定文件分数
        homework_scores[stu_class][student_name][filename] = score
        
        # 保存回文件
        try:
            with open(scores_file, 'w', encoding='utf-8') as f:
                json.dump(homework_scores, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已保存学生分数：班级={stu_class}, 用户={student_name}, 文件={filename}, 分数={score}")
            return jsonify({
                "success": True,
                "message": "分数提交成功",
                "details": {
                    "class": stu_class,
                    "student": student_name,
                    "filename": decoded_filename,
                    "score": score
                }
            })
        except Exception as write_err:
            logger.error(f"保存分数文件时出错: {write_err}")
            return jsonify({"success": False, "error": f"保存分数时出错: {str(write_err)}"}), 500
    
    except Exception as e:
        logger.error(f"处理分数提交时出错: {e}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": f"服务器内部错误: {str(e)}"}), 500



@app.route('/api/get_score/<filename>', methods=['GET'])
def get_score(filename):
    try:
        if not filename :
            return jsonify({"success": False, "error": "缺少必要参数: 文件名或分数"}), 400
        
        decoded_filename = urllib.parse.unquote(filename)
        logger.info(f"解码后的文件名: {decoded_filename}")
        
        # 提取第一个下划线前的内容作为学生姓名
        student_name = decoded_filename.split('_')[0]
        logger.info(f"从文件名提取的学生姓名: {student_name}")
               
        # 获取学生班级
        stu_class = authenticate_user_class(student_name)
        logger.info(f"学生班级: {stu_class}, 学生姓名: {student_name}")
            

        if not stu_class:
            logger.warning(f"找不到用户 {student_name} 的班级信息")
            return jsonify({"success": False, "error": "找不到用户的班级信息"}), 404
            
        logger.info(f"学生班级：{stu_class}")
        
        # 读取分数文件
        scores_file = 'homework_score.json'
        if not os.path.exists(scores_file):
            logger.warning(f"{scores_file} 文件不存在")
            return jsonify({"success": True, "score": None, "message": "没有找到分数记录"})
            
        with open(scores_file, 'r', encoding='utf-8') as f:
            homework_scores = json.load(f)
        
        # 按照班级->用户->文件名的结构查找分数
        score = None
        if (stu_class in homework_scores and 
            student_name in homework_scores[stu_class] and 
            filename in homework_scores[stu_class][student_name]):
            score = homework_scores[stu_class][student_name][filename]
            logger.info(f"找到分数：{score}")
        else:
            logger.info(f"未找到分数记录：班级={stu_class}, 用户={student_name}, 文件={filename}")
            
        # 返回查找到的分数或None
        return jsonify({
            "success": True, 
            "score": score,
            "user": student_name,
            "class": stu_class,
            "filename": filename
        })
        
    except Exception as e:
        logger.error(f"获取分数时出错: {e}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": f"服务器内部错误: {str(e)}"}), 500


@app.route('/api/get_scores', methods=['GET'])
def get_scores():
    try:
        logger.info("Fetching all scores")
        if os.path.exists('homework_score.json'):
            with open('homework_score.json', 'r', encoding='utf-8') as f:
                homework_scores = json.load(f)
            
            # 将嵌套的分数数据结构扁平化为 {filename: score} 格式
            flattened_scores = {}
            for class_name, students in homework_scores.items():
                for student_name, files in students.items():
                    for filename, score in files.items():
                        flattened_scores[filename] = score
            
            logger.info(f"Retrieved and flattened {len(flattened_scores)} scores")
            return jsonify({"success": True, "scores": flattened_scores})
        else:
            logger.warning("homework_score.json does not exist")
            return jsonify({"success": True, "scores": {}})
    except Exception as e:
        logger.error(f"Error retrieving scores: {e}")
        return jsonify({"success": False, "error": "Internal Server Error"}), 500

#######################################################################################################
# 视频播放页面pdf代码
# ... existing code ...

@app.route('/api/videopdf/matches')
def get_pdf_matches():
    """获取视频和PDF的匹配信息"""
    try:
        # 验证用户身份
        user = request.args.get('user')
        key = request.args.get('key')
        token = request.args.get('token')

        if not user or not key:
            return jsonify({'error': 'Missing user or key'}), 401

        if not authenticate_user(user, key):
            return jsonify({'error': 'Invalid user or key'}), 401

        if not verify_access_token(user, token, user_key_config):
            return jsonify({'error': 'Invalid token'}), 401

        # 读取匹配文件
        matches_path = os.path.join(COURSE_VIDEO_BASE, 'video_pdf_matches.json')
        if not os.path.exists(matches_path):
            return jsonify({'error': 'Matches file not found'}), 404

        try:
            with open(matches_path, 'r', encoding='utf-8') as f:
                matches_data = json.load(f)
            return jsonify(matches_data)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing matches file: {str(e)}")
            return jsonify({'error': 'Invalid matches file format'}), 500

    except Exception as e:
        logger.error(f"Error in get_pdf_matches: {str(e)}")
        return jsonify({'error': str(e)}), 500




@app.route('/api/videopdf/<path:folder>/ragfiles/<path:filename>')
def serve_video_pdf(folder, filename):
    """提供视频相关的PDF文件"""
    try:
        # 验证用户身份
        user = request.args.get('user')
        key = request.args.get('key')
        token = request.args.get('token')

        if not user or not key:
            return jsonify({'error': 'Missing user or key'}), 401

        if not authenticate_user(user, key):
            return jsonify({'error': 'Invalid user or key'}), 401

        if not verify_access_token(user, token, user_key_config):
            return jsonify({'error': 'Invalid token'}), 401

        # 构建PDF文件路径
        pdf_path = os.path.join(COURSE_VIDEO_BASE, folder, 'ragfiles', filename)
        logger.info(f"Attempting to serve PDF: {pdf_path}")

        # 验证文件路径安全性
        if not os.path.normpath(pdf_path).startswith(os.path.normpath(COURSE_VIDEO_BASE)):
            logger.warning(f"Attempted path traversal: {pdf_path}")
            return jsonify({'error': 'Invalid file path'}), 403

        # 检查文件是否存在
        if not os.path.exists(pdf_path):
            logger.error(f"PDF file not found: {pdf_path}")
            return jsonify({'error': 'PDF file not found'}), 404

        # 检查文件是否是PDF
        if not pdf_path.lower().endswith('.pdf'):
            logger.warning(f"Invalid file type requested: {pdf_path}")
            return jsonify({'error': 'Invalid file type'}), 400

        try:
            # 设置响应头以优化PDF传输
            response = send_file(
                pdf_path,
                mimetype='application/pdf',
                as_attachment=False,
                download_name=filename
            )
            
            # 添加缓存控制头
            response.headers['Cache-Control'] = 'public, max-age=86400'  # 24小时缓存
            response.headers['Access-Control-Allow-Origin'] = '*'  # 允许跨域访问
            
            logger.info(f"Successfully serving PDF file: {filename}")
            return response

        except Exception as e:
            logger.error(f"Error sending PDF file: {str(e)}")
            return jsonify({'error': 'Error sending file'}), 500

    except Exception as e:
        logger.error(f"Error in serve_video_pdf: {str(e)}")
        return jsonify({'error': str(e)}), 500

# 添加 OPTIONS 请求处理，以支持跨域请求
@app.route('/api/videopdf/<path:folder>/ragfiles/<path:filename>', methods=['OPTIONS'])
def handle_pdf_options(folder, filename):
    response = make_response()
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,OPTIONS')
    return response

##################################################################################
#mermind图片

# 添加新的路由来提供mermindfig文件夹下的HTML文件
@app.route('/api/mermindfig/<path:filename>')
def serve_mermindfig(filename):
    if filename == 'electric.html':
        increment_tool_visits('electric')
    if filename == 'latexeditor.html':
        increment_tool_visits('latex_editor')
    if filename == 'markdown-renderer.html':
        increment_tool_visits('markdown_renderer')
    if filename == 'gen_questions.html':
        increment_tool_visits('gen_questions')
        
    user = request.args.get('user')
    key = request.args.get('key')
    token = request.args.get('token')

    if not user or not key:
        return jsonify({'error': 'Missing user or key'}), 401
    
    if not authenticate_user(user, key):
        return jsonify({'error': 'Invalid user or key'}), 401

    if not verify_access_token(user, token, user_key_config):
        return jsonify({'error': 'Invalid token'}), 401

    try:
        # 构建文件路径
        file_path = os.path.join('mermindfig', filename)
        logger.info(f"Attempting to serve mermindfig file: {file_path}")

        # 验证文件路径安全性
        if not os.path.normpath(file_path).startswith('mermindfig'):
            logger.warning(f"Attempted path traversal: {file_path}")
            return jsonify({'error': 'Invalid file path'}), 403

        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return jsonify({'error': 'File not found'}), 404

        # 检查文件是否是HTML
        if not filename.lower().endswith('.html'):
            logger.warning(f"Invalid file type requested: {file_path}")
            return jsonify({'error': 'Invalid file type'}), 400

        try:
            # 设置响应头
            response = send_file(
                file_path,
                mimetype='text/html',
                as_attachment=False
            )
            # 添加缓存控制头
            response.headers.update({
                'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
                'Pragma': 'no-cache',
                'Expires': '0'
            })           
            logger.info(f"Successfully serving mermindfig file: {filename}")
            return response

        except Exception as e:
            logger.error(f"Error sending file: {str(e)}")
            return jsonify({'error': 'Error sending file'}), 500

    except Exception as e:
        logger.error(f"Error in serve_mermindfig: {str(e)}")
        return jsonify({'error': str(e)}), 500

#############################################################################################################
#初始化文件chat

USER_CONFIG_FILE = 'user_configs.json'
def save_user_config(username, config):
    # 如果文件不存在，创建一个空的字典
    if not os.path.exists(USER_CONFIG_FILE):
        with open(USER_CONFIG_FILE, 'w') as f:
            json.dump({}, f)
    # 读取现有的配置
    with open(USER_CONFIG_FILE, 'r', encoding='utf-8') as f:
        all_configs = json.load(f)
    # 更新用户的配置，保留未更新的参数
    if username not in all_configs:
        all_configs[username] = {}
    all_configs[username].update(config)  # 只更新提供的参数
    # 写回文件
    with open(USER_CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(all_configs, f, indent=4)



@app.route('/api/init_fileschat', methods=['POST'])
def init_fileschat():
    update_config_if_needed()
    try:
        data = request.json
        user = data.get('user', None)
        key = data.get('key', None)
        token = data.get('token', None)
        language = data.get('language', 'Chinese')
        summary_length = data.get('summary_length', 300)
        query_source = data.get('query_source', '用户输入')
        net_method = data.get('net_method', 'duckduckgo')
        search_method = data.get('search_method', '本地')
        deep_search_enabled = data.get('deep_search_enabled', False)
        enable_thinking = data.get('enable_thinking', False)
        selected_doc_ids = data.get('selected_doc_ids', [])

        max_results=data.get('max_results',[])
        # 将 search_method 转换为数字
        if search_method == "本地":
            method = 0
        elif search_method == "联网":
            method = 1
        elif search_method == "聊天":
            method = 3
        elif search_method == "润色":
            method = 4
        elif search_method == "论文搜索":
            method = 5
        elif search_method == "论文搜索1":
            method = 6
        else:
            method = 2  # 默认值本地+联网

        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Invalid user or key: {user}, {key}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Invalid token: {user}, {token}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401

        # 保存用户配置
        user_config = {
            'method': method,
            'net_method': net_method,
            'query_source': query_source,
            'language': language,
            'summary_length': summary_length,
            'deep_search_enabled': deep_search_enabled,
            'enable_thinking': enable_thinking,
            'selected_doc_ids': selected_doc_ids,
            'max_results': max_results
        }
        logger.info(f"Saving user config for {user}: {user_config}")
        save_user_config(user, user_config)

        return jsonify({'success': True, 'message': 'User configuration saved successfully.'})

    except Exception as e:
        logger.error(f"Error in init_fileschat: {str(e)}")
        return jsonify({'success': False, 'message': f'Initialization failed: {str(e)}'}), 500
##############################################################################################################
#ragfiles后端
def get_user_files_with_doc_ids(username, limit=200):
    user_files = []
    doc_ids = {}
    files_with_doc_ids = {}
    # 读取 user_files.json
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
                user_files = user_data.get(username, [])
        except json.JSONDecodeError:
            print(f"警告: {USER_FILES_JSON} 不是有效的 JSON 格式")
        except Exception as e:
            print(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")

    
    if os.path.exists(DOC_IDS_JSON):
        # 使用检测到的编码读取文件
        with open(DOC_IDS_JSON, 'r', encoding='utf-8') as f:
             doc_ids = json.load(f)
    # 筛选出有 doc_id 的文件，并创建文件名到 doc_id 的映射
    for file in reversed(user_files):  # 从最新的文件开始
        if file in doc_ids:
            files_with_doc_ids[file] = doc_ids[file]
            if len(files_with_doc_ids) == limit:  # 只保留最新的 limit 个文件
                break
    return files_with_doc_ids

@app.route('/api/ragfiles', methods=['GET'])
def get_ragfiles():
    username = request.args.get('username')
    if not username:
        return jsonify({"error": "Username is required"}), 400

    # 如果用户是 VIP，则限制文件数量为 100
    if is_user_vip(username):
        files_with_doc_ids = get_user_files_with_doc_ids(username, limit=100)
    else:
        files_with_doc_ids = get_user_files_with_doc_ids(username, limit=9)
    if not files_with_doc_ids:
        return jsonify({"message": "No files found for this user"}), 404

    return jsonify(files_with_doc_ids), 200
############################################################################################
#删除rag文件
def delete_user_file(username, filename):
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                user_files = json.load(f)
                
            if username in user_files and filename in user_files[username]:
                user_files[username].remove(filename)
                
                with open(USER_FILES_JSON, 'w', encoding='utf-8') as f:
                    json.dump(user_files, f, indent=2)
                return True
        except Exception as e:
            print(f"删除文件记录时发生错误: {str(e)}")
    return False

@app.route('/api/del_file', methods=['DELETE'])
def del_file():
    data = request.get_json()
    username = data.get('username')
    filename = data.get('fileName')
    key = data.get('key')
    token = data.get('token')
    if not username or not filename or not key or not token:
        return jsonify({"success": False, "message": "Username, filename, key, and token are required."}), 400

    if not authenticate_user(username, key):
        return jsonify({"success": False, "message": "Invalid user or key"}), 401

    if not verify_access_token(username, token, user_key_config):
        return jsonify({"success": False, "message": "Invalid token"}), 401

    if delete_user_file(username, filename):
        return jsonify({"success": True, "message": "File deleted successfully."}), 200
    else:
        return jsonify({"success": False, "message": "File not found or could not be deleted."}), 404
###############################################################################################################
#根据文章生成知识图谱
@app.route('/api/gen_graph', methods=['POST'])
def handle_gen_graph():
    update_config_if_needed()
    logger.info(f"Starting new graph generation request")
    try:
        # 记录请求数据
        data = request.json
        logger.info(f"Received request data: {data}")
        
        # 获取并记录各个参数
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '') 
        filename = data.get('filename', '')
        doc_id = data.get('doc_id', '')
        dataset_id = data.get('dataset_id', '')
        folder = data.get('folder', '')

        # 构建并检查博客路径
        blog_path = os.path.join(BLOG_DIR, filename)
        logger.info(f"Blog path: {blog_path}")  
        # 加载用户配置
        user_config = load_user_config(user)
        logger.info(f"Loaded user config: {user_config}")
        api_key_rag = user_config.get('api_key_rag', '')
        api_key_type = user_config.get('api_key_type', '')


        # 处理文档ID
        if not doc_id and filename:
            logger.info("No doc_id provided, attempting to upload or get doc_id")
            doc_id = upload_or_get_doc_id(blog_path, kb_id_blog, api_key_blog, parser_id="naive")
            logger.info(f"Retrieved doc_id: {doc_id}")
            if not doc_id:
                logger.error("Failed to get doc_id")
                return jsonify({'success': False, 'message': '正在添加知识库，请稍后再试'}), 500
        
        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
             
        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        # 处理知识图谱生成
        try:
            # 获取文件名（去除扩展名）
            if filename:
                base_filename = os.path.splitext(filename)[0]
            else:
                # 如果没有提供文件名，使用文档ID或当前时间戳
                base_filename = doc_id if doc_id else f"graph_{int(time.time())}"
            
            # 构建保存路径
            save_dir = os.path.join(COURSE_VIDEO_BASE, folder)
            save_path = os.path.join(save_dir, f"{base_filename}_graph.json")
            
            # 检查图谱文件是否已存在
            if os.path.exists(save_path):
                logger.info(f"Knowledge graph file already exists at {save_path}, loading from file")
                
                # 从文件中读取图谱数据
                with open(save_path, 'r', encoding='utf-8') as f:
                    graph_data = json.load(f)
                
                # 将图谱数据转换回原始格式以保持API一致性
                graph_result = ""
                for edge in graph_data.get("edges", []):
                    source = edge.get("source", "")
                    target = edge.get("target", "")
                    relation = edge.get("relation", "")
                    graph_result += f"{source},{target},{{{relation}}}\n"
                
                logger.info(f"Successfully loaded knowledge graph from file")
                logger.info(f"Graph result (first 200 chars): {graph_result[:200]}...")
                
                # 返回成功响应
                return jsonify({
                    'success': True,
                    'graph': graph_result,
                })
            
            if not api_key_type:
                return jsonify({'success': False, 'message': '请先设置API密钥类型'}), 400
            if api_key_type == "deepseek":
                api_key = user_config.get('api_key_deepseek', '')
            elif api_key_type == "siliconflow":
                api_key = user_config.get('api_key_siliconflow', '')
            elif api_key_type == "qwen":
                api_key = user_config.get('api_key_qwen', '')

            # 如果文件不存在，则生成新的知识图谱
            logger.info(f"Knowledge graph file does not exist, generating new graph")
            logger.info(f"Starting knowledge graph generation with dataset_id: {dataset_id}, doc_id: {doc_id}")
            
            # 创建聊天机器人实例
            logger.info("Initializing chatbot for graph generation")
            chatbot = SmartChatbot( 
                api_key_rag=api_key_rag,
                api_key=api_key,
                api_key_type=api_key_type,
                folder=folder
            )
            logger.info(f"Initialized chatbot")
            
            # 获取知识图谱
            graph_result = chatbot.get_graph(
                dataset_ids=dataset_id if isinstance(dataset_id, list) else [dataset_id],
                document_ids=doc_id if isinstance(doc_id, list) else [doc_id]
            )
            
            # 确保 graph_result 是 UTF-8 编码的字符串
            if isinstance(graph_result, str):
                graph_result = graph_result.encode('utf-8', errors='replace').decode('utf-8')
            
            logger.info(f"Successfully generated knowledge graph")
            # 使用安全的方式记录日志
            if graph_result:
                safe_preview = graph_result[:200].encode('utf-8', errors='replace').decode('utf-8')
                logger.info(f"Graph result (first 200 chars): {safe_preview}...")
            
            # 保存知识图谱到JSON文件
            if graph_result and folder:
                try:
                    # 创建保存路径
                    save_dir = os.path.join(COURSE_VIDEO_BASE, folder)
                    os.makedirs(save_dir, exist_ok=True)
                    
                    # 获取文件名（去除扩展名）
                    if filename:
                        base_filename = os.path.splitext(filename)[0]
                    else:
                        # 如果没有提供文件名，使用文档ID或当前时间戳
                        base_filename = doc_id if doc_id else f"graph_{int(time.time())}"
                    
                    # 构建完整的保存路径
                    save_path = os.path.join(save_dir, f"{base_filename}_graph.json")

                    # 将图谱数据转换为JSON格式
                    graph_data = {
                        "edges": [],
                        "metadata": {
                            "generated_at": datetime.now().isoformat(),
                            "source_document": filename,
                            "document_id": doc_id
                        }
                    }

                    # 解析图谱结果并添加到edges数组
                    for line in graph_result.strip().split('\n'):
                        if line.strip():
                            parts = line.split(',', 2)
                            if len(parts) == 3:
                                source, target, relation = parts
                                # 提取关系中的文本（去除花括号）
                                relation = relation.strip('{}')
                                graph_data["edges"].append({
                                    "source": source,
                                    "target": target,
                                    "relation": relation
                                })

                    # 写入JSON文件
                    with open(save_path, 'w', encoding='utf-8') as f:
                        json.dump(graph_data, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"Knowledge graph saved to {save_path}")
                except Exception as save_error:
                    logger.error(f"Error saving knowledge graph to file: {str(save_error)}")
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'graph': graph_result
            })
            
        except Exception as graph_error:
            # 确保错误消息是 UTF-8 编码的
            error_msg = str(graph_error).encode('utf-8', errors='replace').decode('utf-8')
            logger.error(f"Error during graph generation: {error_msg}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Graph generation error: {error_msg}'}), 500
            
    except Exception as e:
        logger.error(f"Unexpected error in handle_gen_graph: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500
    finally:
        logger.info("=== Completed graph generation request ===")


@app.route('/api/update_graph', methods=['POST'])
def handle_update_graph():
    update_config_if_needed()
    logger.info(f"Starting graph update request")
    try:
        # 记录请求数据
        data = request.json
        logger.info(f"Received update request data: {data}")
        
        # 获取并记录各个参数
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '') 
        filename = data.get('filename', '')
        folder = data.get('folder', '')
        graph_data = data.get('graph_data', '')
        
        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
             
        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        if_vip_user = is_user_vip(user)
        # 验证必要参数
        if not filename or not folder or not graph_data:
            missing_params = []
            if not filename: missing_params.append('filename')
            if not folder: missing_params.append('folder')
            if not graph_data: missing_params.append('graph_data')
            
            logger.error(f"Missing required parameters: {', '.join(missing_params)}")
            return jsonify({'success': False, 'message': f'Missing required parameters: {", ".join(missing_params)}'}), 400
        
        try:
            # 获取文件名（去除扩展名）
            base_filename = os.path.splitext(filename)[0]
            
            # 构建保存路径
            save_dir = os.path.join(COURSE_VIDEO_BASE, folder)
            os.makedirs(save_dir, exist_ok=True)
            save_path = os.path.join(save_dir, f"{base_filename}_graph.json")
            
            # 将图谱数据转换为JSON格式
            graph_data_lines = graph_data.strip().split('\n')
            
            graph_json = {
                "edges": [],
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "source_document": filename,
                    "updated_by": user
                }
            }
            
            # 解析图谱数据
            for line in graph_data_lines:
                if line.strip():
                    parts = line.split(',', 2)
                    if len(parts) == 3:
                        source, target, relation = parts
                        # 提取关系中的文本（去除花括号）
                        relation = relation.strip('{}')
                        graph_json["edges"].append({
                            "source": source,
                            "target": target,
                            "relation": relation
                        })
            
            # 写入JSON文件
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(graph_json, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Updated knowledge graph saved to {save_path}")
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'message': 'Knowledge graph updated successfully',
                'if_vip_user': if_vip_user
            })
            
        except Exception as update_error:
            logger.error(f"Error during graph update: {str(update_error)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Graph update error: {str(update_error)}'}), 500
            
    except Exception as e:
        logger.error(f"Unexpected error in handle_update_graph: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500
    finally:
        logger.info("=== Completed graph update request ===")


@app.route('/api/delete_graph', methods=['POST'])
def handle_delete_graph():
    update_config_if_needed()
    logger.info(f"Starting graph deletion request")
    try:
        # 记录请求数据
        data = request.json
        logger.info(f"Received delete request data: {data}")
        
        # 获取并记录各个参数
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '') 
        filename = data.get('filename', '')
        folder = data.get('folder', '')
        
        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
             
        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        # 检查用户是否为VIP
        if_vip_user = is_user_vip(user)
        if not if_vip_user:
            logger.error(f"Non-VIP user attempted to delete graph: {user}")
            return jsonify({'success': False, 'message': 'Only VIP users can delete knowledge graphs'}), 403
        
        # 验证必要参数
        if not filename or not folder:
            missing_params = []
            if not filename: missing_params.append('filename')
            if not folder: missing_params.append('folder')
            
            logger.error(f"Missing required parameters: {', '.join(missing_params)}")
            return jsonify({'success': False, 'message': f'Missing required parameters: {", ".join(missing_params)}'}), 400
        
        try:
            # 获取文件名（去除扩展名）
            base_filename = os.path.splitext(filename)[0]
            
            # 构建文件路径
            graph_path = os.path.join(COURSE_VIDEO_BASE, folder, f"{base_filename}_graph.json")
            
            # 检查文件是否存在
            if not os.path.exists(graph_path):
                logger.warning(f"Knowledge graph file not found: {graph_path}")
                return jsonify({
                    'success': True,
                    'message': 'Knowledge graph does not exist or already deleted',
                    'if_vip_user': if_vip_user
                })
            
            # 删除文件
            os.remove(graph_path)
            logger.info(f"Knowledge graph deleted: {graph_path}")
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'message': 'Knowledge graph deleted successfully',
                'if_vip_user': if_vip_user
            })
            
        except Exception as delete_error:
            logger.error(f"Error during graph deletion: {str(delete_error)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Graph deletion error: {str(delete_error)}'}), 500
            
    except Exception as e:
        logger.error(f"Unexpected error in handle_delete_graph: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500
    finally:
        logger.info("=== Completed graph deletion request ===")



@app.route('/api/merge_graphs', methods=['POST'])
def handle_merge_graphs():
    update_config_if_needed()
    logger.info(f"Starting graph merging request")
    try:
        # 记录请求数据
        data = request.json
        logger.info(f"Received merge request data: {data}")
        
        # 获取并记录各个参数
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '') 
        folder = data.get('folder', '')
        
        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
             
        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        # 检查用户是否为VIP
        if_vip_user = is_user_vip(user)
        
        # 验证必要参数
        if not folder:
            logger.error("Missing required parameter: folder")
            return jsonify({'success': False, 'message': 'Missing required parameter: folder'}), 400
        
        try:
            # 构建文件夹路径
            folder_path = os.path.join(COURSE_VIDEO_BASE, folder)
            
            # 检查文件夹是否存在
            if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                logger.error(f"Folder not found: {folder_path}")
                return jsonify({'success': False, 'message': 'Folder not found'}), 404
            
            # 检查是否存在已合并的图谱文件
            existing_merged_files = [f for f in os.listdir(folder_path) if f.startswith('merged_graph_') and f.endswith('.json')]
            use_existing_file = False
            existing_merged_file = None
            
            if existing_merged_files:
                # 尝试从文件名中提取时间戳
                newest_merged_file = None
                newest_timestamp = 0
                
                for merged_file in existing_merged_files:
                    try:
                        # 从文件名中提取时间戳 (merged_graph_1234567890.json)
                        timestamp_str = merged_file.replace('merged_graph_', '').replace('.json', '')
                        timestamp = int(timestamp_str)
                        
                        if timestamp > newest_timestamp:
                            newest_timestamp = timestamp
                            newest_merged_file = merged_file
                    except ValueError:
                        # 如果文件名格式不符合预期，跳过
                        continue
                
                if newest_merged_file:
                    newest_merged_path = os.path.join(folder_path, newest_merged_file)
                    
                    # 从文件内容中读取生成时间
                    try:
                        with open(newest_merged_path, 'r', encoding='utf-8') as f:
                            file_data = json.load(f)
                            generated_at = file_data.get('metadata', {}).get('generated_at')
                            
                            if generated_at:
                                # 解析ISO格式的时间字符串
                                generated_time = datetime.fromisoformat(generated_at)
                                current_time = datetime.now()
                                time_diff = current_time - generated_time
                                time_diff_days = time_diff.total_seconds() / (24 * 3600)  # 转换为天数
                                
                                logger.info(f"Found existing merged file: {newest_merged_file}, created {time_diff_days:.2f} days ago")
                                
                                # 如果文件创建时间在1天内，直接使用该文件
                                if time_diff_days < 1.0:
                                    use_existing_file = True
                                    existing_merged_file = newest_merged_file
                                    logger.info(f"Using existing merged file as it's less than 1 day old")
                    except Exception as e:
                        logger.warning(f"Error reading timestamp from file {newest_merged_file}: {str(e)}")
                        # 如果无法从文件内容读取时间，回退到使用文件名中的时间戳
                        file_creation_time = newest_timestamp
                        current_time = int(time.time())
                        time_diff_days = (current_time - file_creation_time) / (24 * 3600)
                        
                        if time_diff_days < 1.0:
                            use_existing_file = True
                            existing_merged_file = newest_merged_file
                            logger.info(f"Using existing merged file based on filename timestamp, created {time_diff_days:.2f} days ago")
            
            # 如果有可用的现有文件，直接返回该文件内容
            if use_existing_file and existing_merged_file:
                existing_merged_path = os.path.join(folder_path, existing_merged_file)
                
                with open(existing_merged_path, 'r', encoding='utf-8') as f:
                    merged_graph_data = json.load(f)
                
                # 将图谱数据转换为原始格式
                graph_result = ""
                for edge in merged_graph_data.get("edges", []):
                    source = edge.get("source", "")
                    target = edge.get("target", "")
                    relation = edge.get("relation", "")
                    graph_result += f"{source},{target},{{{relation}}}\n"
                
                logger.info(f"Successfully loaded existing merged knowledge graph")
                
                # 返回成功响应
                return jsonify({
                    'success': True,
                    'message': 'Using existing merged knowledge graph',
                    'graph': graph_result,
                    'merged_file': existing_merged_file,
                    'edge_count': len(merged_graph_data.get("edges", [])),
                    'source_count': merged_graph_data.get("metadata", {}).get("source_count", 0),
                    'if_vip_user': if_vip_user,
                    'is_cached': True
                })
            
            # 查找所有以 _graph.json 结尾的文件
            graph_files = [f for f in os.listdir(folder_path) if f.endswith('_graph.json') and not f.startswith('merged_graph_')]
            
            if not graph_files:
                logger.warning(f"No graph files found in folder: {folder_path}")
                return jsonify({
                    'success': False, 
                    'message': 'No knowledge graph files found in the specified folder'
                }), 404
            
            logger.info(f"Found {len(graph_files)} graph files in folder {folder}")
            
            # 合并所有图谱
            merged_edges = []
            merged_sources = set()  # 用于跟踪已合并的源文档
            
            for graph_file in graph_files:
                file_path = os.path.join(folder_path, graph_file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        graph_data = json.load(f)
                    
                    # 记录源文档
                    source_doc = graph_data.get('metadata', {}).get('source_document', graph_file)
                    merged_sources.add(source_doc)
                    
                    # 合并边
                    edges = graph_data.get('edges', [])
                    merged_edges.extend(edges)
                    logger.info(f"Added {len(edges)} edges from {graph_file}")
                    
                except Exception as e:
                    logger.warning(f"Error reading graph file {graph_file}: {str(e)}")
                    continue
            
            # 去重边（基于源节点和目标节点的组合，忽略方向）
            unique_edges = []
            edge_dict = {}  # 用字典替代集合，存储节点对到边的映射
            
            for edge in merged_edges:
                source = edge.get('source', '')
                target = edge.get('target', '')
                relation = edge.get('relation', '')
                
                # 创建无方向的节点对（按字母顺序排序以确保一致性）
                node_pair = tuple(sorted([source, target]))
                
                # 检查这对节点是否已有边
                if node_pair in edge_dict:
                    # 获取已存在的边的关系描述
                    existing_relation = edge_dict[node_pair].get('relation', '')
                    
                    # 如果当前边的关系描述更长（更详细），则替换已有边
                    if len(relation) > len(existing_relation):
                        # 找到并移除旧边
                        for i, old_edge in enumerate(unique_edges):
                            if tuple(sorted([old_edge.get('source', ''), old_edge.get('target', '')])) == node_pair:
                                unique_edges.pop(i)
                                break
                        # 添加新边
                        unique_edges.append(edge)
                        edge_dict[node_pair] = edge
                else:
                    # 如果这对节点之间还没有边，则添加当前边
                    edge_dict[node_pair] = edge
                    unique_edges.append(edge)
                    
            logger.info(f"Merged graph contains {len(unique_edges)} unique edges from {len(merged_sources)} sources")
            
            # 创建合并后的图谱数据
            merged_graph_data = {
                "edges": unique_edges,
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "merged_by": user,
                    "source_documents": list(merged_sources),
                    "source_count": len(merged_sources)
                }
            }
            
            # 保存合并后的图谱
            merged_filename = f"merged_graph_{int(time.time())}.json"
            merged_path = os.path.join(folder_path, merged_filename)
            
            with open(merged_path, 'w', encoding='utf-8') as f:
                json.dump(merged_graph_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Merged knowledge graph saved to {merged_path}")
            
            # 删除旧的合并文件
            if existing_merged_files:
                for old_file in existing_merged_files:
                    try:
                        old_file_path = os.path.join(folder_path, old_file)
                        os.remove(old_file_path)
                        logger.info(f"Deleted old merged file: {old_file}")
                    except Exception as del_err:
                        logger.warning(f"Failed to delete old merged file {old_file}: {str(del_err)}")
            
            # 将合并后的图谱转换为原始格式以保持API一致性
            graph_result = ""
            for edge in unique_edges:
                source = edge.get('source', '')
                target = edge.get('target', '')
                relation = edge.get('relation', '')
                graph_result += f"{source},{target},{{{relation}}}\n"
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'message': f'Successfully merged {len(graph_files)} knowledge graphs',
                'graph': graph_result,
                'merged_file': merged_filename,
                'edge_count': len(unique_edges),
                'source_count': len(merged_sources),
                'if_vip_user': if_vip_user,
                'is_cached': False
            })
            
        except Exception as merge_error:
            logger.error(f"Error during graph merging: {str(merge_error)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Graph merging error: {str(merge_error)}'}), 500
            
    except Exception as e:
        logger.error(f"Unexpected error in handle_merge_graphs: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500
    finally:
        logger.info("=== Completed graph merging request ===")




###############################################################################################################
# 题目生成
###############################################################################################################

def validate_questions_data(questions_data, question_type):
    """验证题目数据的格式和完整性"""
    try:
        if not isinstance(questions_data, dict):
            return {'valid': False, 'message': '题目数据必须是字典格式'}

        if question_type == 'choice':
            return validate_choice_questions_data(questions_data)
        elif question_type == 'calculation':
            return validate_calculation_questions_data(questions_data)
        else:
            return {'valid': False, 'message': f'不支持的题目类型: {question_type}'}

    except Exception as e:
        logger.error(f"验证题目数据时出错: {str(e)}")
        return {'valid': False, 'message': f'验证过程出错: {str(e)}'}

def validate_choice_questions_data(questions_data):
    """验证选择题数据"""
    if 'questions' not in questions_data:
        return {'valid': False, 'message': '缺少questions字段'}

    questions = questions_data['questions']
    if not isinstance(questions, list):
        return {'valid': False, 'message': 'questions必须是数组'}

    if len(questions) == 0:
        return {'valid': False, 'message': '题目数组不能为空'}

    for i, question in enumerate(questions):
        if not isinstance(question, dict):
            return {'valid': False, 'message': f'第{i+1}题必须是对象格式'}

        required_fields = ['id', 'question', 'options', 'correct_answer', 'explanation']
        for field in required_fields:
            if field not in question:
                return {'valid': False, 'message': f'第{i+1}题缺少{field}字段'}

        # 验证选项
        if not isinstance(question['options'], list) or len(question['options']) < 2:
            return {'valid': False, 'message': f'第{i+1}题的选项必须是至少包含2个元素的数组'}

        # 验证正确答案
        if question['correct_answer'] not in ['A', 'B', 'C', 'D']:
            return {'valid': False, 'message': f'第{i+1}题的正确答案必须是A、B、C、D中的一个'}

    return {'valid': True, 'message': '选择题数据验证通过'}

def validate_calculation_questions_data(questions_data):
    """验证计算题数据"""
    if 'calculation_questions' not in questions_data:
        return {'valid': False, 'message': '缺少calculation_questions字段'}

    questions = questions_data['calculation_questions']
    if not isinstance(questions, list):
        return {'valid': False, 'message': 'calculation_questions必须是数组'}

    if len(questions) == 0:
        return {'valid': False, 'message': '计算题数组不能为空'}

    for i, question in enumerate(questions):
        if not isinstance(question, dict):
            return {'valid': False, 'message': f'第{i+1}题必须是对象格式'}

        required_fields = ['id', 'question', 'solution_steps', 'final_answer', 'key_points']
        for field in required_fields:
            if field not in question:
                return {'valid': False, 'message': f'第{i+1}题缺少{field}字段'}

        # 验证solution_steps
        solution_steps = question['solution_steps']
        if isinstance(solution_steps, str):
            try:
                solution_steps = json.loads(solution_steps)
            except json.JSONDecodeError:
                return {'valid': False, 'message': f'第{i+1}题的solution_steps字符串格式不正确'}

        if not isinstance(solution_steps, list):
            return {'valid': False, 'message': f'第{i+1}题的solution_steps必须是数组'}

        # 验证final_answer
        final_answer = question['final_answer']
        if isinstance(final_answer, dict):
            # 兼容旧格式
            if 'value' not in final_answer or 'unit' not in final_answer:
                return {'valid': False, 'message': f'第{i+1}题的final_answer旧格式必须包含value和unit字段'}
        elif isinstance(final_answer, str):
            # 新的简化格式
            if not final_answer.strip():
                return {'valid': False, 'message': f'第{i+1}题的final_answer不能为空'}
        else:
            return {'valid': False, 'message': f'第{i+1}题的final_answer必须是字符串或包含value和unit字段的对象'}

        # 验证key_points
        key_points = question['key_points']
        if isinstance(key_points, str):
            try:
                key_points = json.loads(key_points)
            except json.JSONDecodeError:
                key_points = [key_points]

        if not isinstance(key_points, list):
            return {'valid': False, 'message': f'第{i+1}题的key_points必须是数组'}

    return {'valid': True, 'message': '计算题数据验证通过'}

@app.route('/api/user_questions', methods=['GET'])
def get_user_questions():
    """获取用户的所有题目文件"""
    try:
        # 从查询参数获取用户信息
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')

        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401

        # 扫描题目文件
        root_save_dir = 'gen_questions_data'
        user_questions = []

        if os.path.exists(root_save_dir):
            # 扫描根目录下的文件
            for filename in os.listdir(root_save_dir):
                if filename.endswith('.json') and filename.startswith(f"{user}_"):
                    file_path = os.path.join(root_save_dir, filename)
                    try:
                        # 解析文件名获取信息
                        parts = filename.replace('.json', '').split('_')
                        if len(parts) >= 3:
                            timestamp = parts[1]
                            question_type = parts[2]

                            # 读取文件内容获取更多信息
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = json.load(f)

                            # 获取文件修改时间
                            mod_time = os.path.getmtime(file_path)
                            mod_time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')

                            # 计算题目数量
                            question_count = 0
                            if question_type == 'choice' and 'questions' in content:
                                question_count = len(content['questions'])
                            elif question_type == 'calc' and 'calculation_questions' in content:
                                question_count = len(content['calculation_questions'])

                            # 获取课程信息 - 优先从course_name字段获取，然后从metadata.folder，最后从folder字段
                            course_name = content.get('course_name') or \
                                         (content.get('metadata', {}).get('folder') if content.get('metadata') else None) or \
                                         content.get('folder', '未知课程')

                            user_questions.append({
                                'filename': filename,
                                'filepath': file_path,
                                'timestamp': timestamp,
                                'question_type': question_type,
                                'question_type_name': '选择题' if question_type == 'choice' else '计算题',
                                'question_count': question_count,
                                'course_name': course_name,
                                'modified_time': mod_time_str,
                                'file_size': os.path.getsize(file_path)
                            })
                    except Exception as e:
                        logger.error(f"Error reading question file {filename}: {e}")
                        continue

        # 按修改时间倒序排列（最新的在前面）
        user_questions.sort(key=lambda x: x['timestamp'], reverse=True)

        return jsonify({
            'success': True,
            'questions': user_questions,
            'total_count': len(user_questions)
        })

    except Exception as e:
        logger.error(f"Error getting user questions: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/question_detail', methods=['GET'])
def get_question_detail():
    """获取特定题目文件的详细内容"""
    try:
        # 从查询参数获取信息
        user = request.args.get('user', '')
        key = request.args.get('key', '')
        token = request.args.get('token', '')
        filename = request.args.get('filename', '')

        # 验证用户
        if not authenticate_user(user, key):
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            return jsonify({'success': False, 'message': 'Invalid token'}), 401

        # 验证文件名安全性
        if not filename or '..' in filename or '/' in filename or '\\' in filename:
            return jsonify({'success': False, 'message': 'Invalid filename'}), 400

        # 确保文件属于当前用户
        if not filename.startswith(f"{user}_"):
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        # 构建文件路径
        root_save_dir = 'gen_questions_data'
        file_path = os.path.join(root_save_dir, filename)

        if not os.path.exists(file_path):
            return jsonify({'success': False, 'message': 'File not found'}), 404

        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = json.load(f)

        return jsonify({
            'success': True,
            'content': content,
            'filename': filename
        })

    except Exception as e:
        logger.error(f"Error getting question detail: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/delete_question_file', methods=['POST'])
def delete_question_file():
    """删除用户的题目文件"""
    try:
        data = request.json
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '')
        filename = data.get('filename', '')

        # 验证用户
        if not authenticate_user(user, key):
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            return jsonify({'success': False, 'message': 'Invalid token'}), 401

        # 验证文件名安全性
        if not filename or '..' in filename or '/' in filename or '\\' in filename:
            return jsonify({'success': False, 'message': 'Invalid filename'}), 400

        # 确保文件属于当前用户
        if not filename.startswith(f"{user}_"):
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        # 构建文件路径
        root_save_dir = 'gen_questions_data'
        file_path = os.path.join(root_save_dir, filename)

        if not os.path.exists(file_path):
            return jsonify({'success': False, 'message': 'File not found'}), 404

        # 删除文件
        os.remove(file_path)
        logger.info(f"Deleted question file: {file_path}")

        return jsonify({
            'success': True,
            'message': 'File deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting question file: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/gen_questions', methods=['GET', 'POST'])
def handle_gen_questions():
    """处理生成题目的请求"""
    update_config_if_needed()
    logger.info(f"Starting new questions generation request")
    try:
        # 记录请求数据
        data = request.json
        logger.info(f"Received request data: {data}")
        
        # 获取并记录各个参数
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '')
        filename = data.get('filename', '')
        doc_id = data.get('doc_id', '')
        dataset_id = data.get('dataset_id', '')
        folder = data.get('folder', '')
        question_type = data.get('question_type', 'choice')  # 'choice' 或 'calculation'
        num_questions = data.get('num_questions', '3')
        logger.info(f"后台接收的num_questions: {num_questions}")

        # 新增：处理多选文档ID
        selected_doc_ids = data.get('selected_doc_ids', '')
        if selected_doc_ids:
            # 如果有selected_doc_ids，使用它们
            if isinstance(selected_doc_ids, str):
                doc_ids_list = [id.strip() for id in selected_doc_ids.split(',') if id.strip()]
            else:
                doc_ids_list = selected_doc_ids
        elif doc_id:
            # 兼容原有的单个doc_id
            doc_ids_list = [doc_id]
        else:
            doc_ids_list = []
        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
             
        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        # 加载用户配置
        user_config = load_user_config(user)
        logger.info(f"Loaded user config: {user_config}")
        api_key_rag = user_config.get('api_key_rag', '')
        api_key_type = user_config.get('api_key_type', '')

        
        # 处理题目生成
        try:
            # 获取文件名（去除扩展名）
            if filename:
                base_filename = os.path.splitext(filename)[0]
            else:
                # 如果没有提供文件名，使用文档ID或当前时间戳
                base_filename = f"questions_{int(time.time())}"
            
            # 构建保存路径 - 支持多种保存目录
            # 判断是否存在original_save_floder，不存在就使用root_save_dir
            root_save_dir = 'gen_questions_data'
            original_save_dir = os.path.join(COURSE_VIDEO_BASE, folder, "questions")
            original_save_floder=os.path.join(COURSE_VIDEO_BASE, folder)
            # 检查原有目录是否存在，如果不存在则使用根目录
            if os.path.exists(original_save_floder):
                # 使用原有目录
                save_dir = original_save_dir
                os.makedirs(save_dir, exist_ok=True)
                question_type_suffix = "choice" if question_type == "choice" else "calc"
                save_path = os.path.join(save_dir, f"{base_filename}_{question_type_suffix}_questions.json")
            else:
                # 使用根目录
                save_dir = root_save_dir
                os.makedirs(save_dir, exist_ok=True)
                # 使用时间戳作为文件名前缀，避免冲突
                timestamp = int(time.time())
                question_type_suffix = "choice" if question_type == "choice" else "calc"
                save_path = os.path.join(save_dir, f"{user}_{timestamp}_{question_type_suffix}.json")
            
            # 检查题目文件是否已存在
            if os.path.exists(save_path):
                logger.info(f"Questions file already exists at {save_path}, loading from file")
                
                # 从文件中读取题目数据
                with open(save_path, 'r', encoding='utf-8') as f:
                    questions_data = json.load(f)
                
                logger.info(f"Successfully loaded questions from file")
                
                # 返回成功响应
                return jsonify({
                    'success': True,
                    'questions': questions_data,
                })
            
            # 如果文件不存在，则生成新的题目
            logger.info(f"Questions file does not exist, generating new questions")
            if not api_key_type:
                return jsonify({'success': False, 'message': '请先设置API密钥类型'}), 400
            if api_key_type == "deepseek":
                api_key = user_config.get('api_key_deepseek', '')
            elif api_key_type == "siliconflow":
                api_key = user_config.get('api_key_siliconflow', '')
            elif api_key_type == "qwen":
                api_key = user_config.get('api_key_qwen', '')
            else:
                return jsonify({'success': False,'message': '无效的API密钥类型'}), 400

            # 创建问题生成器实例
            logger.info("Initializing question generator")
            question_generator = QuestionGenerator(
                api_key=api_key,
                api_key_rag=api_key_rag,
                api_key_type=api_key_type,
                folder=folder
            )
            logger.info(f"Initialized question generator")
            
            # 生成题目
            if question_type == "choice":
                try:
                    logger.info(f"Generating choice questions with num_questions={num_questions}, doc_ids={doc_ids_list}")
                    questions_data = question_generator.generate_questions(
                        dataset_ids=dataset_id if isinstance(dataset_id, list) else [dataset_id],
                        document_ids=doc_ids_list,
                        num_questions=num_questions
                    )
                except Exception as e:
                    logger.error(f"Error generating choice questions: {str(e)}\nStack trace: {traceback.format_exc()}")
                    return jsonify({'success': False, 'message': f'生成选择题失败: {str(e)}'}), 500
            else:
                try:
                    logger.info(f"Generating calculation questions with num_questions={num_questions}, doc_ids={doc_ids_list}")
                    # 确保num_questions是整数
                    if not isinstance(num_questions, int):
                        try:
                            num_questions = int(num_questions)
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid num_questions value, using default: {num_questions}")
                    questions_data = question_generator.generate_calculation_questions(
                        dataset_ids=dataset_id if isinstance(dataset_id, list) else [dataset_id],
                        document_ids=doc_ids_list,
                        num_questions=num_questions
                    )
                except Exception as e:
                    logger.error(f"Error generating calculation questions: {str(e)}\nStack trace: {traceback.format_exc()}")
                    return jsonify({'success': False, 'message': f'生成计算题失败: {str(e)}'}), 500
            
            # 检查是否有错误
            if "error" in questions_data:
                logger.error(f"Error generating questions: {questions_data['error']}")
                return jsonify({'success': False, 'message': questions_data['error']}), 500

            # 验证生成的题目数据
            validation_result = validate_questions_data(questions_data, question_type)
            if not validation_result['valid']:
                logger.error(f"Generated questions validation failed: {validation_result['message']}")
                return jsonify({'success': False, 'message': f'生成的题目数据验证失败: {validation_result["message"]}'}), 500

            logger.info(f"Successfully generated and validated questions")
            
            # 保存题目到JSON文件
            try:
                # 添加元数据
                questions_data["metadata"] = {
                    "generated_at": datetime.now().isoformat(),
                    "source_document": filename,
                    "document_id": doc_id,
                    "question_type": question_type,
                    "folder":folder,
                    "num_questions": num_questions
                }

                # 添加 course_name 字段到题目数据的根级别，用于前端显示
                questions_data["course_name"] = folder
                
                # 确保保存路径是安全的
                try:
                    # 写入JSON文件
                    with open(save_path, 'w', encoding='utf-8') as f:
                        json.dump(questions_data, f, ensure_ascii=False, indent=2)

                    logger.info(f"Questions saved to {save_path}")
                except (OSError, IOError) as file_error:
                    logger.error(f"File system error when saving questions: {str(file_error)}")
                    # 即使保存失败，我们仍然返回生成的题目
                    logger.warning("Continuing despite file save error")
            except Exception as save_error:
                logger.error(f"Error saving questions to file: {str(save_error)}\nStack trace: {traceback.format_exc()}")
                # 即使保存失败，我们仍然返回生成的题目
                logger.warning("Continuing despite metadata/save error")
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'questions': questions_data
            })
            
        except Exception as question_error:
            # 确保错误消息是 UTF-8 编码的
            error_msg = str(question_error).encode('utf-8', errors='replace').decode('utf-8')
            logger.error(f"Error during question generation: {error_msg}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Question generation error: {error_msg}'}), 500
            
    except Exception as e:
        logger.error(f"Unexpected error in handle_gen_questions: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500
    finally:
        logger.info("=== Completed question generation request ===")

@app.route('/api/update_questions', methods=['POST'])
def handle_update_questions():
    """处理更新题目的请求"""
    update_config_if_needed()
    logger.info(f"Starting questions update request")
    try:
        # 记录请求数据
        data = request.json
        logger.info(f"Received update request data: {data}")
        
        # 获取并记录各个参数
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '') 
        filename = data.get('filename', '')
        folder = data.get('folder', '')
        question_type = data.get('question_type', 'choice')  # 'choice' 或 'calculation'
        questions_data = data.get('questions_data', {})
        
        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
             
        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        # 检查用户是否为VIP
        if_vip_user = is_user_vip(user)
        if not if_vip_user:
            logger.error(f"Non-VIP user attempted to update questions: {user}")
            return jsonify({'success': False, 'message': 'Only VIP users can update questions'}), 403
        
        # 验证必要参数
        if not filename or not folder or not questions_data:
            missing_params = []
            if not filename: missing_params.append('filename')
            if not folder: missing_params.append('folder')
            if not questions_data: missing_params.append('questions_data')
            
            logger.error(f"Missing required parameters: {', '.join(missing_params)}")
            return jsonify({'success': False, 'message': f'Missing required parameters: {", ".join(missing_params)}'}), 400
        
        try:
            # 处理计算题的solution_steps，确保它是数组而不是字符串
            if question_type == 'calculation' and 'calculation_questions' in questions_data:
                logger.info(f"Processing {len(questions_data['calculation_questions'])} calculation questions")
                for i, question in enumerate(questions_data['calculation_questions']):
                    logger.info(f"Processing question {i+1}: {question.get('id', 'no-id')}")

                    if 'solution_steps' in question:
                        solution_steps = question['solution_steps']
                        logger.info(f"Question {i+1} solution_steps type: {type(solution_steps)}")

                        if isinstance(solution_steps, str):
                            logger.info(f"Question {i+1} solution_steps is string, attempting to parse: {solution_steps[:200]}...")
                            try:
                                parsed_steps = json.loads(solution_steps)
                                question['solution_steps'] = parsed_steps
                                logger.info(f"Question {i+1} solution_steps parsed successfully, type: {type(parsed_steps)}, length: {len(parsed_steps) if isinstance(parsed_steps, list) else 'not-list'}")
                            except json.JSONDecodeError as e:
                                logger.error(f"Question {i+1} failed to parse solution_steps as JSON: {str(e)}")
                                logger.error(f"Question {i+1} problematic solution_steps content: {solution_steps}")
                                return jsonify({'success': False, 'message': f'题目{i+1}的solution_steps格式不正确，请确保它是有效的JSON数组: {str(e)}'}), 400
                        elif isinstance(solution_steps, list):
                            logger.info(f"Question {i+1} solution_steps is already a list with {len(solution_steps)} items")
                        else:
                            logger.warning(f"Question {i+1} solution_steps is neither string nor list: {type(solution_steps)}")
                            return jsonify({'success': False, 'message': f'题目{i+1}的solution_steps必须是数组或JSON字符串'}), 400
                    else:
                        logger.info(f"Question {i+1} has no solution_steps field")
            
            # 获取文件名（去除扩展名）
            base_filename = os.path.splitext(filename)[0]
            
            # 构建保存路径 - 支持多种保存目录
            # 判断是否存在original_save_dir，不存在就使用root_save_dir
            root_save_dir = 'gen_questions_data'
            original_save_dir = os.path.join(COURSE_VIDEO_BASE, folder, "questions")

            # 检查原有目录是否存在，如果不存在则使用根目录
            if os.path.exists(original_save_dir):
                # 使用原有目录
                save_dir = original_save_dir
                question_type_suffix = "choice" if question_type == "choice" else "calc"
                save_path = os.path.join(save_dir, f"{base_filename}_{question_type_suffix}_questions.json")
            else:
                # 使用根目录
                save_dir = root_save_dir
                os.makedirs(save_dir, exist_ok=True)
                # 使用时间戳作为文件名前缀，避免冲突
                timestamp = int(time.time())
                question_type_suffix = "choice" if question_type == "choice" else "calc"
                save_path = os.path.join(save_dir, f"questions_{timestamp}_{question_type_suffix}.json")
            
            # 更新元数据
            if "metadata" in questions_data:
                questions_data["metadata"]["updated_at"] = datetime.now().isoformat()
                questions_data["metadata"]["updated_by"] = user
            else:
                questions_data["metadata"] = {
                    "updated_at": datetime.now().isoformat(),
                    "updated_by": user,
                    "question_type": question_type
                }
            
            # 写入JSON文件
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(questions_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Updated questions saved to {save_path}")
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'message': 'Questions updated successfully',
                'if_vip_user': if_vip_user
            })
            
        except Exception as update_error:
            logger.error(f"Error during questions update: {str(update_error)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Questions update error: {str(update_error)}'}), 500
            
    except Exception as e:
        logger.error(f"Unexpected error in handle_update_questions: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500
    finally:
        logger.info("=== Completed questions update request ===")

@app.route('/api/delete_questions', methods=['POST'])
def handle_delete_questions():
    """处理删除题目的请求"""
    update_config_if_needed()
    logger.info(f"Starting questions deletion request")
    try:
        # 记录请求数据
        data = request.json
        logger.info(f"Received delete request data: {data}")
        
        # 获取并记录各个参数
        user = data.get('user', '')
        key = data.get('key', '')
        token = data.get('token', '') 
        filename = data.get('filename', '')
        folder = data.get('folder', '')
        question_type = data.get('question_type', 'choice')  # 'choice' 或 'calculation'
        
        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401
             
        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        # 检查用户是否为VIP
        if_vip_user = is_user_vip(user)
        if not if_vip_user:
            logger.error(f"Non-VIP user attempted to delete questions: {user}")
            return jsonify({'success': False, 'message': 'Only VIP users can delete questions'}), 403
        
        # 验证必要参数
        if not filename or not folder:
            missing_params = []
            if not filename: missing_params.append('filename')
            if not folder: missing_params.append('folder')
            
            logger.error(f"Missing required parameters: {', '.join(missing_params)}")
            return jsonify({'success': False, 'message': f'Missing required parameters: {", ".join(missing_params)}'}), 400
        
        try:
            # 获取文件名（去除扩展名）
            base_filename = os.path.splitext(filename)[0]
            
            # 构建文件路径 - 支持多种保存目录
            question_type_suffix = "choice" if question_type == "choice" else "calc"

            # 优先检查根目录的gen_questions_data，如果不存在则检查原有路径
            root_save_dir = 'gen_questions_data'
            original_questions_path = os.path.join(COURSE_VIDEO_BASE, folder, "questions", f"{base_filename}_{question_type_suffix}_questions.json")

            # 检查根目录是否有匹配的文件（使用时间戳格式）
            questions_path = None
            if os.path.exists(root_save_dir):
                # 查找匹配的文件
                for filename in os.listdir(root_save_dir):
                    if filename.endswith(f"_{question_type_suffix}.json"):
                        questions_path = os.path.join(root_save_dir, filename)
                        break

            # 如果根目录没有找到，使用原有路径
            if not questions_path or not os.path.exists(questions_path):
                questions_path = original_questions_path
            
            # 检查文件是否存在
            if not os.path.exists(questions_path):
                logger.warning(f"Questions file not found: {questions_path}")
                return jsonify({
                    'success': True,
                    'message': 'Questions file does not exist or already deleted',
                    'if_vip_user': if_vip_user
                })
            
            # 删除文件
            os.remove(questions_path)
            logger.info(f"Questions file deleted: {questions_path}")
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'message': 'Questions deleted successfully',
                'if_vip_user': if_vip_user
            })
            
        except Exception as delete_error:
            logger.error(f"Error during questions deletion: {str(delete_error)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Questions deletion error: {str(delete_error)}'}), 500
            
    except Exception as e:
        logger.error(f"Unexpected error in handle_delete_questions: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500
    finally:
        logger.info("=== Completed questions deletion request ===") 








@app.route('/api/init_questions', methods=['POST'])
def init_questions():
    try:
        data = request.json
        filename = data.get('filename', '').strip()
        folder = data.get('folder', '').strip()  # 确保去除空白字符
        user = data.get('user', '').strip()
        key = data.get('key', '').strip()
        token = data.get('token', '').strip()
        question_type = data.get('question_type', 'choice')  # 'choice' 或 'calculation'
        
        logger.info(f"Received init request - filename: {filename}, folder: {folder}, user: {user}, question_type: {question_type}")
        
        # 增加参数验证
        if not all([filename, user, key, token]):
            missing = []
            if not filename: missing.append('filename')
            if not user: missing.append('user')
            if not key: missing.append('key')
            if not token: missing.append('token')
            logger.error(f"Missing required parameters: {', '.join(missing)}")
            return jsonify({'success': False, 'message': f'Missing required parameters: {", ".join(missing)}'}), 400
        
        # 清理文件名，移除任何可能导致路径问题的字符
        filename = secure_filename_with_extension(filename)
        if folder:
            folder = os.path.normpath(folder).replace('..', '')
        
        logger.info(f"Sanitized params - filename: {filename}, folder: {folder}, user: {user}")

        # 验证用户
        if not authenticate_user(user, key):
            logger.error(f"Authentication failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid user or key'}), 401

        # 验证令牌
        if not verify_access_token(user, token, user_key_config):
            logger.error(f"Token verification failed for user: {user}")
            return jsonify({'success': False, 'message': 'Invalid token'}), 401

        if_vip_user = is_user_vip(user)
        logger.info(f"User {user} VIP status: {if_vip_user}")
        
        # 检查题目文件是否已存在
        try:
            base_filename = os.path.splitext(filename)[0]
            question_type_suffix = "choice" if question_type == "choice" else "calc"
            
            # 确保目录存在 - 支持多种保存目录
            # 判断是否存在original_questions_dir，不存在就使用root_save_dir
            root_save_dir = 'gen_questions_data'
            original_questions_dir = os.path.join(COURSE_VIDEO_BASE, folder, "questions")

            # 检查原有目录是否存在，如果不存在则使用根目录
            if os.path.exists(original_questions_dir):
                # 使用原有目录
                questions_dir = original_questions_dir
                if not os.path.exists(questions_dir):
                    logger.info(f"Creating questions directory: {questions_dir}")
                    os.makedirs(questions_dir, exist_ok=True)
                questions_path = os.path.join(questions_dir, f"{base_filename}_{question_type_suffix}_questions.json")
            else:
                # 使用根目录
                questions_dir = root_save_dir
                if not os.path.exists(questions_dir):
                    logger.info(f"Creating questions directory: {questions_dir}")
                    os.makedirs(questions_dir, exist_ok=True)
                # 使用时间戳作为文件名前缀，避免冲突
                timestamp = int(time.time())
                questions_path = os.path.join(questions_dir, f"questions_{timestamp}_{question_type_suffix}.json")
            questions_path = os.path.abspath(os.path.normpath(questions_path))
            
            logger.info(f"Constructed questions_path: {questions_path}")
            
            if not os.path.isabs(questions_path):
                logger.error(f"Invalid path construction: Path is not absolute: {questions_path}")
                return jsonify({'success': False, 'message': 'Invalid path construction'}), 500
                
        except Exception as e:
            logger.error(f"Path construction error: {str(e)}\nStack trace: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Error in path construction: {str(e)}'}), 500
            
        # 获取字幕文件路径
        try:
            # 使用原始文件名（未经过secure_filename处理）来构建字幕文件名
            original_filename = data.get('filename', '').strip()
            base_filename = os.path.splitext(original_filename)[0]
            subtitle_name = base_filename + ".json"
            
            # 构建字幕文件路径
            subtitle_path = os.path.join(COURSE_VIDEO_BASE, folder, subtitle_name) if folder else os.path.join(COURSE_VIDEO_BASE, subtitle_name)
            subtitle_path = os.path.abspath(os.path.normpath(subtitle_path))
            
            logger.info(f"Constructed subtitle_path: {subtitle_path}")
            
            # 如果文件不存在，尝试使用安全文件名
            if not os.path.exists(subtitle_path):
                logger.warning(f"Subtitle file not found with original name, trying with sanitized name")
                sanitized_base_filename = os.path.splitext(filename)[0]
                sanitized_subtitle_name = sanitized_base_filename + ".json"
                sanitized_subtitle_path = os.path.join(COURSE_VIDEO_BASE, folder, sanitized_subtitle_name) if folder else os.path.join(COURSE_VIDEO_BASE, sanitized_subtitle_name)
                sanitized_subtitle_path = os.path.abspath(os.path.normpath(sanitized_subtitle_path))
                
                logger.info(f"Constructed sanitized subtitle_path: {sanitized_subtitle_path}")
                
                if os.path.exists(sanitized_subtitle_path):
                    logger.info(f"Found subtitle file with sanitized name")
                    subtitle_path = sanitized_subtitle_path
            
            if not os.path.isabs(subtitle_path):
                logger.error(f"Invalid subtitle path construction: Path is not absolute: {subtitle_path}")
                return jsonify({'success': False, 'message': 'Invalid subtitle path construction'}), 500
                
        except Exception as e:
            logger.error(f"Subtitle path construction error: {str(e)}\nStack trace: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Error in subtitle path construction: {str(e)}'}), 500

        # 检查题目文件是否已存在
        try:
            # questions_path 已经在上面设置了，不需要重新定义
            questions_exist = os.path.exists(questions_path)

            logger.info(f"Questions file path: {questions_path}")
            logger.info(f"Questions file exists: {questions_exist}")

            # 获取dataset_id
            dataset_id = None
            try:
                dataset_id = get_matching_ids(folder, COURSE_VIDEO_BASE)
                logger.info(f"Found dataset_id: {dataset_id}")
            except Exception as e:
                logger.warning(f"Failed to get dataset_id: {str(e)}")

            # 获取doc_id
            doc_id = None
            if os.path.exists(subtitle_path):
                try:
                    # 加载用户配置获取API密钥
                    user_config = load_user_config(user)
                    api_key_rag = user_config.get('api_key_rag', '')

                    if api_key_rag:
                        doc_id = uploadvideo_or_get_doc_id(subtitle_path, folder, api_key_rag, parser_id="naive")
                        logger.info(f"Found doc_id: {doc_id}")
                    else:
                        logger.warning("No RAG API key found in user config")
                except Exception as e:
                    logger.warning(f"Failed to get doc_id: {str(e)}")
            else:
                logger.warning(f"Subtitle file not found: {subtitle_path}")

            # 返回初始化结果
            return jsonify({
                'success': True,
                'questions_exist': questions_exist,
                'questions_path': questions_path,
                'dataset_id': dataset_id,
                'doc_id': doc_id,
                'subtitle_path': subtitle_path,
                'subtitle_exists': os.path.exists(subtitle_path),
                'if_vip_user': if_vip_user
            })

        except Exception as e:
            logger.error(f"Error in questions initialization: {str(e)}\nStack trace: {traceback.format_exc()}")
            return jsonify({'success': False, 'message': f'Error in questions initialization: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"Unexpected error in init_questions: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500


###################################################################################################################################
# 统计class信息
# 在适当的位置添加以下导入
# 添加获取所有班级的API
@app.route('/api/get_all_classes', methods=['GET'])
def get_all_classes():
    try:
        # 读取config.yaml获取所有带class属性的用户
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 提取所有不同的class值
        classes = set()
        if 'users' in config:
            for user in config['users']:
                if 'class' in user:
                    classes.add(user['class'])
        
        # 检查class_user_data目录中的JSON文件
        class_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                     'class_user_data', 'class_user_data')
        if os.path.exists(class_data_dir):
            for filename in os.listdir(class_data_dir):
                if filename.endswith('.json'):
                    class_name = filename[:-5]  # 移除.json后缀
                    classes.add(class_name)
        
        return jsonify({'classes': sorted(list(classes))})
    except Exception as e:
        logger.error(f"Error getting classes: {str(e)}")
        return jsonify({'error': str(e)}), 500



# 添加获取班级学生数据的API
@app.route('/api/get_class_data', methods=['GET'])
def get_class_data():
    try:
        class_name = request.args.get('class')
        if not class_name:
            return jsonify({'error': '缺少class参数'}), 400
        
        # 验证用户身份
        user = request.args.get('user')
        key = request.args.get('key')
        token = request.args.get('token')

        if not user or not key:
            return jsonify({'error': 'Missing user or key'}), 401
        
        if not authenticate_user(user, key):
            return jsonify({'error': 'Invalid user or key'}), 401

        if not verify_access_token(user, token, user_key_config):
            return jsonify({'error': 'Invalid token'}), 401
        
        # 构建JSON文件路径
        class_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                     'class_user_data', 'class_user_data')
        json_file_path = os.path.join(class_data_dir, f"{class_name}.json")
        
        # 从config.yaml获取该班级的所有学生
        all_students = {}
        try:
            with open('config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if 'users' in config:
                for user_data in config['users']:
                    if user_data.get('class') == class_name:
                        # 获取学生的姓名信息（如果有）
                        student_name = user_data.get('name', user_data['username'])
                        all_students[user_data['username']] = {
                            'user': user_data['username'],
                            'name': student_name,  # 添加学生姓名
                            'class': class_name,
                            'has_records': False,  # 标记是否有浏览记录
                            'chat_views': [],
                            'video_views': []
                        }
                
                logger.info(f"Found {len(all_students)} students in config.yaml for class {class_name}")
            else:
                logger.warning("No users found in config.yaml")
        except Exception as e:
            logger.error(f"Error reading config.yaml: {str(e)}")
        
        # 读取JSON文件中的访问记录（如果存在）
        if os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    students_data = json.load(f)
                    
                    logger.info(f"Found {len(students_data)} student records in {json_file_path}")
                    
                    # 合并访问记录到所有学生数据中
                    for student in students_data:
                        username = student.get('user')
                        chat_views = student.get('chat_views', [])
                        video_views = student.get('video_views', [])
                        
                        if username in all_students:
                            # 更新已存在学生的访问记录
                            all_students[username]['chat_views'] = chat_views
                            all_students[username]['video_views'] = video_views
                            # 如果有任何浏览记录，标记为True
                            all_students[username]['has_records'] = bool(chat_views or video_views)
                            logger.info(f"Updated records for student: {username}")
                        else:
                            # 添加有记录但不在config中的学生
                            student['has_records'] = bool(chat_views or video_views)
                            all_students[username] = student
                            logger.info(f"Added student with records not in config: {username}")
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON in file: {json_file_path}")
        else:
            logger.info(f"No records file found at {json_file_path}")
        
        # 转换字典为列表
        students_list = list(all_students.values())
        
        # 按照姓名排序（如果没有姓名则按用户名排序）
        students_list.sort(key=lambda x: x.get('name', x.get('user', '')))
        
        logger.info(f"Returning {len(students_list)} students for class {class_name}")
        return jsonify({'students': students_list})
    except Exception as e:
        logger.error(f"Error getting class data: {str(e)}")
        return jsonify({'error': str(e)}), 500
# 添加新的路由来提供mermindfig文件夹下的HTML文件


@app.route('/api/class_user_data/<path:filename>')
def serve_class_user_data(filename):

    user = request.args.get('user')
    key = request.args.get('key')
    token = request.args.get('token')

    if not user or not key:
        return jsonify({'error': 'Missing user or key'}), 401
    
    if not authenticate_user(user, key):
        return jsonify({'error': 'Invalid user or key'}), 401

    if not verify_access_token(user, token, user_key_config):
        return jsonify({'error': 'Invalid token'}), 401

    try:
        # 构建文件路径
        file_path = os.path.join('class_user_data', filename)
        logger.info(f"Attempting to serve mermindfig file: {file_path}")

        # 验证文件路径安全性
        if not os.path.normpath(file_path).startswith('class_user_data'):
            logger.warning(f"Attempted path traversal: {file_path}")
            return jsonify({'error': 'Invalid file path'}), 403

        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return jsonify({'error': 'File not found'}), 404

        # 检查文件是否是HTML
        if not filename.lower().endswith('.html'):
            logger.warning(f"Invalid file type requested: {file_path}")
            return jsonify({'error': 'Invalid file type'}), 400

        try:
            # 设置响应头
            response = send_file(
                file_path,
                mimetype='text/html',
                as_attachment=False
            )
            
            # 添加缓存控制头
            response.headers.update({
                'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
                'Pragma': 'no-cache',
                'Expires': '0'
            })
            
            logger.info(f"Successfully serving class_user_data file: {filename}")
            return response

        except Exception as e:
            logger.error(f"Error sending file: {str(e)}")
            return jsonify({'error': 'Error sending file'}), 500

    except Exception as e:
        logger.error(f"Error in class_user_data: {str(e)}")
        return jsonify({'error': str(e)}), 500





















@app.route('/api/sync_class_users', methods=['GET'])
def sync_class_users():
    """
    将config.yaml中的用户按班级同步到class_user_data/*.json文件中
    确保每个班级的JSON文件包含该班级的所有学生
    仅包含用户名和班级信息，如果用户已存在则保留原有数据
    """
    try:
        # 验证用户身份
        user = request.args.get('user')
        key = request.args.get('key')
        token = request.args.get('token')

        if not user or not key:
            return jsonify({'error': 'Missing user or key'}), 401
        
        if not authenticate_user(user, key):
            return jsonify({'error': 'Invalid user or key'}), 401

        if not verify_access_token(user, token, user_key_config):
            return jsonify({'error': 'Invalid token'}), 401
        
        # 读取config.yaml获取所有用户和他们的班级
        try:
            with open('config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error reading config.yaml: {str(e)}")
            return jsonify({'error': f'Error reading config.yaml: {str(e)}'}), 500
        
        if 'credentials' not in config or 'usernames' not in config['credentials']:
            return jsonify({'message': 'No users found in config.yaml', 'updated': 0})
        
        # 按班级组织用户
        classes = {}
        for username, user_data in config['credentials']['usernames'].items():
            if 'class' in user_data and user_data['class']:
                class_name = user_data['class']
                if class_name not in classes:
                    classes[class_name] = []
                
                # 仅提取用户名和班级信息
                student_info = {
                    'user': username,
                    'class': class_name
                }
                classes[class_name].append(student_info)
        
        # 确保class_user_data目录存在
        class_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                     'class_user_data', 'class_user_data')
        os.makedirs(class_data_dir, exist_ok=True)
        
        # 更新或创建每个班级的JSON文件
        updated_files = 0
        for class_name, students in classes.items():
            json_file_path = os.path.join(class_data_dir, f"{class_name}.json")
            existing_data = []
            
            # 尝试读取现有的JSON文件
            if os.path.exists(json_file_path):
                try:
                    with open(json_file_path, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON in file: {json_file_path}")
                    existing_data = []
            
            # 获取现有用户名列表
            existing_usernames = {student.get('user') for student in existing_data if student.get('user')}
            
            # 添加新用户（不在现有数据中的用户）
            new_users_added = False
            for student in students:
                username = student['user']
                if username not in existing_usernames:
                    existing_data.append(student)
                    new_users_added = True
            
            # 只有当有新用户添加时才写入文件
            if new_users_added or not os.path.exists(json_file_path):
                with open(json_file_path, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, ensure_ascii=False, indent=2)
                    updated_files += 1
        
        return jsonify({
            'message': f'Successfully synchronized user data to class JSON files',
            'updated': updated_files,
            'classes': list(classes.keys())
        })
        
    except Exception as e:
        logger.error(f"Error in sync_class_users: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['GET'])
def analyze():
    # 获取参数
    class_name = request.args.get('class')
    user=request.args.get('user')
    key=request.args.get('key')
    token=request.args.get('token')
    # 获取是否强制刷新参数
    force_refresh = request.args.get('force_refresh', 'false').lower() == 'true'
    if not user or not user=="admin":
        return jsonify({'error': 'Missing user or key'}), 401
    
    if not authenticate_user(user, key):
        return jsonify({'error': 'Invalid user or key'}), 401

    if not verify_access_token(user, token, user_key_config):
        return jsonify({'error': 'Invalid token'}), 401


    # 加载用户配置
    adminuser="admin"
    user_config = load_user_config(adminuser)
    logger.info(f"Loaded user analyze config: {user_config}")
    api_key_siliconflow = user_config.get('api_key_siliconflow', '')
    api_key_deepseek = user_config.get('api_key_deepseek', '')
    api_key_qwen = user_config.get('api_key_qwen', '')
    api_key_type = user_config.get('api_key_type', '')
    api_key = api_key_deepseek if api_key_type == "deepseek" else api_key_siliconflow if api_key_type == "siliconflow" else api_key_qwen if api_key_type == "qwen" else ""
    api_key_rag = user_config.get('api_key_rag', '')   
    # 获取用户class值

    # 检查班级名称
    if not class_name:
        return jsonify({"success": False, "message": "缺少班级名称参数"}), 400
    
    # 分析问题
    analyzer = QuestionAnalyzer(api_key=api_key, api_key_type=api_key_type)
    
    logger.info(f"分析班级问题: {class_name}" + (" (强制刷新)" if force_refresh else ""))
    result = analyzer.analyze_class_questions(class_name, force_refresh=force_refresh)
    
    # 返回结果
    if result.get('success', False):
        return jsonify(result), 200
    else:
        return jsonify(result), 400




@app.route('/api/get_homework_scores', methods=['GET'])
def get_homework_scores():
    """获取指定班级和学生的作业分数"""
    # 获取请求参数（班级和学生，均为可选）
    class_name = request.args.get('class', None)
    student_name = request.args.get('student', None)
    
    
    try:
        # 读取作业分数文件
        file_path = 'homework_score.json'  # 根据实际路径调整
        
        if not os.path.exists(file_path):
            return jsonify({"error": "Homework score file not found"}), 404
            
        with open(file_path, 'r', encoding='utf-8') as file:
            all_scores = json.load(file)
        
        # 根据参数过滤数据
        result = {}
        
        # 如果指定了班级
        if class_name:
            if class_name in all_scores:
                class_data = all_scores[class_name]
                
                # 如果还指定了学生
                if student_name:
                    if student_name in class_data:
                        result = {
                            "class": class_name,
                            "student": student_name,
                            "homework": class_data[student_name]
                        }
                    else:
                        return jsonify({"error": f"Student {student_name} not found in class {class_name}"}), 404
                else:
                    # 只指定了班级，返回整个班级数据
                    result = {
                        "class": class_name,
                        "students": class_data
                    }
            else:
                return jsonify({"error": f"Class {class_name} not found"}), 404
        else:
            # 没有指定班级，返回所有数据
            result = all_scores
            
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 如果您需要一个额外的端点来获取所有班级的作业统计信息
@app.route('/api/get_homework_stats', methods=['GET'])
def get_homework_stats():
    """获取作业统计信息（每个学生的作业数量和平均分）"""
    # 获取请求参数
    class_name = request.args.get('class', None)
    
    
    try:
        # 读取作业分数文件
        file_path = 'homework_score.json'
        
        if not os.path.exists(file_path):
            return jsonify({"error": "Homework score file not found"}), 404
            
        with open(file_path, 'r', encoding='utf-8') as file:
            all_scores = json.load(file)
        
        # 如果未指定班级，返回错误
        if not class_name:
            return jsonify({"error": "Class name is required"}), 400
            
        if class_name not in all_scores:
            return jsonify({"error": f"Class {class_name} not found"}), 404
            
        class_data = all_scores[class_name]
        stats = {}
        
        # 首先找出最大作业数量
        max_homework_count = 0
        for student, homeworks in class_data.items():
            homework_count = len(homeworks)
            if homework_count > max_homework_count:
                max_homework_count = homework_count
        # 计算每个学生的作业数量和平均分
        for student, homeworks in class_data.items():
            homework_count = len(homeworks)
            total_score = sum(homeworks.values())
            avg_score = total_score / max_homework_count if homework_count > 0 else 0
            
            stats[student] = {
                "count": homework_count,
                "average": round(avg_score, 2)
            }
            
        return jsonify({
            "class": class_name,
            "stats": stats
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/circuit/')
def circuit_simulator():
    increment_tool_visits('circuit')
    circuit_path = os.path.join(SCRIPT_DIR, "tool", "circuit", "index.html")
    return send_file(circuit_path)

##################################################################################



CHAT_LOGS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'chat_logs')

def load_chat_data():
    """加载所有聊天记录数据"""
    all_chats = []
    
    # 查找所有JSON文件
    json_files = glob.glob(os.path.join(CHAT_LOGS_DIR, '*.json'))
    
    for file_path in json_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 提取文件名中的日期
            filename = os.path.basename(file_path)
            file_date = filename.replace('.json', '')
            
            # 处理每个聊天记录
            for chat_id, chat_data in data.items():
                chat_record = {
                    'id': chat_id,
                    'file_date': file_date,
                    'user': chat_data.get('user', ''),
                    'user_class': chat_data.get('user_class', ''),
                    'time': chat_data.get('time', ''),
                    'chat_seq': chat_data.get('chat_seq', ''),
                    'message': chat_data.get('message', ''),
                    'response': chat_data.get('response', ''),
                    'filename': chat_data.get('filename', ''),
                    'method': chat_data.get('method', ''),
                    'api_key_type': chat_data.get('api_key_type', ''),
                    'tokens': extract_tokens(chat_data.get('response', ''))
                }
                all_chats.append(chat_record)
                
        except Exception as e:
            print(f"Error loading file {file_path}: {e}")
            continue
    
    return all_chats

def extract_tokens(response_text):
    """从response中提取token消耗信息"""
    if not response_text:
        return 0
    
    # 查找类似 "本次消耗tokens:1259" 的模式
    token_pattern = r'本次消耗tokens?[:：]\s*(\d+)'
    match = re.search(token_pattern, response_text)
    
    if match:
        return int(match.group(1))
    return 0

@app.route('/api/chats_log', methods=['GET'])
def get_chats_log():
    """获取聊天记录API（新接口）"""
    try:
        # 获取查询参数
        user_filter = request.args.get('user', '').strip()
        user_class_filter = request.args.get('user_class', '').strip()
        search_query = request.args.get('search', '').strip()
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        # 加载所有数据
        all_chats = load_chat_data()

        # 过滤数据
        filtered_chats = []
        for chat in all_chats:
            # 用户过滤
            if user_filter and user_filter.lower() not in chat['user'].lower():
                continue

            # 用户类别过滤
            if user_class_filter and user_class_filter.lower() not in chat['user_class'].lower():
                continue

            # 搜索过滤（在message和response中搜索）
            if search_query:
                search_text = (chat['message'] + ' ' + chat['response']).lower()
                if search_query.lower() not in search_text:
                    continue

            filtered_chats.append(chat)

        # 按时间排序（降序，最新的在前）
        filtered_chats.sort(key=lambda x: x['time'], reverse=True)

        # 分页
        total = len(filtered_chats)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_chats = filtered_chats[start:end]



        response = jsonify({
            'success': True,
            'data': paginated_chats,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })

        # 添加禁用缓存的HTTP头
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        return response

    except Exception as e:
        app.logger.error(f"获取聊天记录失败: {str(e)}")
        error_response = jsonify({
            'success': False,
            'error': str(e)
        })
        # 错误响应也禁用缓存
        error_response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        error_response.headers['Pragma'] = 'no-cache'
        error_response.headers['Expires'] = '0'
        return error_response, 500

@app.route('/api/stats_log', methods=['GET'])
def get_stats_log():
    """获取统计信息API（新接口）"""
    try:
        all_chats = load_chat_data()

        # 获取查询参数
        user_filter = request.args.get('user', '').strip()

        # 如果指定了用户，则只统计该用户的数据
        if user_filter:
            user_chats = [chat for chat in all_chats if chat['user'] == user_filter]
            user_tokens = sum(chat['tokens'] for chat in user_chats)

            # 返回用户特定的统计信息
            return jsonify({
                'success': True,
                'stats': {
                    'total_chats': len(user_chats),
                    'total_users': 1,
                    'users': [user_filter],
                    'user_classes': list(set(chat['user_class'] for chat in user_chats if chat['user_class'])),
                    'total_tokens': user_tokens,
                    'user_tokens': user_tokens,  # 添加用户特定的Token统计
                    'date_stats': {}
                }
            })

        # 全局统计信息（管理员模式）
        total_chats = len(all_chats)
        users = list(set(chat['user'] for chat in all_chats if chat['user']))
        user_classes = list(set(chat['user_class'] for chat in all_chats if chat['user_class']))
        total_tokens = sum(chat['tokens'] for chat in all_chats)

        # 按日期统计
        date_stats = {}
        for chat in all_chats:
            date = chat['time'][:10] if chat['time'] else 'unknown'
            if date not in date_stats:
                date_stats[date] = 0
            date_stats[date] += 1

        return jsonify({
            'success': True,
            'stats': {
                'total_chats': total_chats,
                'total_users': len(users),
                'users': users,
                'user_classes': user_classes,
                'total_tokens': total_tokens,
                'date_stats': date_stats
            }
        })

    except Exception as e:
        app.logger.error(f"获取统计信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/delete_chat', methods=['POST'])
def delete_chat():
    """删除指定chat_id的对话记录（需认证，普通用户只能删自己，管理员可删所有）"""
    try:
        data = request.get_json() or request.form
        chat_id = data.get('chat_id', '').strip()
        user = data.get('user', '').strip()
        key = data.get('key', '').strip()
        token = data.get('token', '').strip()
        if not chat_id or not user or not key or not token:
            return jsonify({'success': False, 'error': 'Missing chat_id, user, key, or token'}), 400

        # 用户认证
        if not authenticate_user(user, key):
            return jsonify({'success': False, 'error': 'Invalid user or key'}), 401
        if not verify_access_token(user, token, user_key_config):
            return jsonify({'success': False, 'error': 'Invalid token'}), 401

        is_admin = (user == 'admin')

        CHAT_LOGS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'chat_logs')
        json_files = glob.glob(os.path.join(CHAT_LOGS_DIR, '*.json'))
        found = False
        for file_path in json_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    chat_data = json.load(f)
                if chat_id in chat_data:
                    # 权限校验：普通用户只能删自己，管理员可删所有
                    chat_owner = chat_data[chat_id].get('user', '')
                    if not is_admin and chat_owner != user:
                        return jsonify({'success': False, 'error': 'Permission denied: cannot delete others\' chat'}), 403
                    del chat_data[chat_id]
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(chat_data, f, ensure_ascii=False, indent=2)
                    found = True
                    logger.info(f"Deleted chat_id {chat_id} from {file_path}")
                    return jsonify({'success': True, 'message': f'Deleted chat_id {chat_id} from {os.path.basename(file_path)}'})
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {str(e)}")
                continue
        if not found:
            return jsonify({'success': False, 'error': f'chat_id {chat_id} not found'}), 404
    except Exception as e:
        logger.error(f"删除对话记录失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/edit_chat', methods=['POST'])
def edit_chat():
    """编辑指定chat_id的对话response（需认证，普通用户只能改自己，管理员可改所有）"""
    try:
        data = request.get_json() or request.form
        chat_id = data.get('chat_id', '').strip()
        user = data.get('user', '').strip()
        key = data.get('key', '').strip()
        token = data.get('token', '').strip()
        new_response = data.get('response', '')
        if not chat_id or not user or not key or not token or new_response is None:
            return jsonify({'success': False, 'error': 'Missing chat_id, user, key, token, or response'}), 400

        # 用户认证
        if not authenticate_user(user, key):
            return jsonify({'success': False, 'error': 'Invalid user or key'}), 401
        if not verify_access_token(user, token, user_key_config):
            return jsonify({'success': False, 'error': 'Invalid token'}), 401

        is_admin = (user == 'admin')

        CHAT_LOGS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'chat_logs')
        json_files = glob.glob(os.path.join(CHAT_LOGS_DIR, '*.json'))
        found = False
        for file_path in json_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    chat_data = json.load(f)
                if chat_id in chat_data:
                    # 权限校验：普通用户只能改自己，管理员可改所有
                    chat_owner = chat_data[chat_id].get('user', '')
                    if not is_admin and chat_owner != user:
                        return jsonify({'success': False, 'error': 'Permission denied: cannot edit others\' chat'}), 403
                    chat_data[chat_id]['response'] = new_response
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(chat_data, f, ensure_ascii=False, indent=2)
                    found = True
                    logger.info(f"Edited response for chat_id {chat_id} in {file_path}")
                    return jsonify({'success': True, 'message': f'Edited response for chat_id {chat_id} in {os.path.basename(file_path)}'})
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {str(e)}")
                continue
        if not found:
            return jsonify({'success': False, 'error': f'chat_id {chat_id} not found'}), 404
    except Exception as e:
        logger.error(f"编辑对话记录失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

######################################################################################################



# 登录和注册接口已移至app.py，避免重复定义





def get_user_files_with_doc_ids(username, limit=200):
    """获取用户文件及其doc_ids"""
    user_files = []
    doc_ids = {}
    files_with_doc_ids = {}

    # 读取 user_files.json
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
                user_files = user_data.get(username, [])
        except json.JSONDecodeError:
            print(f"警告: {USER_FILES_JSON} 不是有效的 JSON 格式")
        except Exception as e:
            print(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")

    if os.path.exists(DOC_IDS_JSON):
        with open(DOC_IDS_JSON, 'r', encoding='utf-8') as f:
             doc_ids = json.load(f)

    # 筛选出有 doc_id 的文件
    for file in reversed(user_files):
        if file in doc_ids:
            files_with_doc_ids[file] = doc_ids[file]
            if len(files_with_doc_ids) == limit:
                break

    return files_with_doc_ids

def get_user_config(username):
    """获取用户配置"""
    if not os.path.exists(USER_CONFIG_FILE):
        return {}

    try:
        with open(USER_CONFIG_FILE, 'r', encoding='utf-8') as f:
            all_configs = json.load(f)
            return all_configs.get(username, {})
    except Exception as e:
        print(f"读取用户配置时出错: {str(e)}")
        return {}

def save_user_config(username, config_data):
    """保存用户配置"""
    if not os.path.exists(USER_CONFIG_FILE):
        with open(USER_CONFIG_FILE, 'w') as f:
            json.dump({}, f)

    with open(USER_CONFIG_FILE, 'r', encoding='utf-8') as f:
        all_configs = json.load(f)

    if username not in all_configs:
        all_configs[username] = {}
    all_configs[username].update(config_data)

    with open(USER_CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(all_configs, f, indent=4, ensure_ascii=False)

# 访问记录文件路径
ACCESS_LOG_FILE = 'access_log.json'

def load_access_log():
    """加载访问记录"""
    if not os.path.exists(ACCESS_LOG_FILE):
        return {
            'total_visits': 0,
            'user_visits': {}
        }
    try:
        with open(ACCESS_LOG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading access log: {str(e)}")
        return {
            'total_visits': 0,
            'user_visits': {}
        }

def save_access_log(access_data):
    """保存访问记录"""
    try:
        with open(ACCESS_LOG_FILE, 'w', encoding='utf-8') as f:
            json.dump(access_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"Error saving access log: {str(e)}")

def record_root_access(username=None):
    """记录根路径访问"""
    try:
        access_data = load_access_log()
        current_time = datetime.now()
        current_time_str = current_time.isoformat()

        # 防重复记录：检查是否在5秒内有相同用户的访问记录
        should_record = True
        user_key = username or 'anonymous'

        if user_key in access_data['user_visits']:
            last_visit_str = access_data['user_visits'][user_key]['last_visit']
            try:
                last_visit_time = datetime.fromisoformat(last_visit_str)
                time_diff = (current_time - last_visit_time).total_seconds()
                if time_diff < 5:  # 5秒内的重复访问不记录
                    should_record = False
                    logger.info(f"Skipped duplicate access for user: {user_key} (within 5 seconds)")
            except ValueError:
                # 如果时间格式解析失败，继续记录
                pass

        if should_record:
            # 增加总访问次数
            access_data['total_visits'] += 1

            # 记录用户访问信息
            if username:
                if username not in access_data['user_visits']:
                    access_data['user_visits'][username] = {
                        'count': 0,
                        'first_visit': current_time_str,
                        'last_visit': current_time_str
                    }

                access_data['user_visits'][username]['count'] += 1
                access_data['user_visits'][username]['last_visit'] = current_time_str
            else:
                # 匿名访问
                if 'anonymous' not in access_data['user_visits']:
                    access_data['user_visits']['anonymous'] = {
                        'count': 0,
                        'first_visit': current_time_str,
                        'last_visit': current_time_str
                    }

                access_data['user_visits']['anonymous']['count'] += 1
                access_data['user_visits']['anonymous']['last_visit'] = current_time_str

            save_access_log(access_data)
            logger.info(f"Recorded root access for user: {username or 'anonymous'}")

    except Exception as e:
        logger.error(f"Error recording root access: {str(e)}")

# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由
# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 添加根路径路由# 
@app.route('/')
def root_index():
    """根路径重定向到主应用"""
    return redirect('/main/')






@app.route('/api/access-log')
def get_access_log():
    """获取访问记录API（仅管理员可访问）"""
    try:
        # 检查用户是否已登录
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'}), 401

        username = session['username']

        # 检查是否为管理员
        if username != 'admin':
            return jsonify({'success': False, 'message': '权限不足，仅管理员可访问'}), 403

        # 获取访问记录
        access_data = load_access_log()

        # 格式化用户访问数据，按最近访问时间排序
        user_visits_list = []
        for user, data in access_data['user_visits'].items():
            user_visits_list.append({
                'username': user,
                'count': data['count'],
                'first_visit': data['first_visit'],
                'last_visit': data['last_visit']
            })

        # 按最近访问时间降序排序（最新的访问记录排在第一行）
        user_visits_list.sort(key=lambda x: x['last_visit'], reverse=True)

        response = jsonify({
            'success': True,
            'data': {
                'total_visits': access_data['total_visits'],
                'user_visits': user_visits_list
            }
        })

        # 禁用缓存
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        return response

    except Exception as e:
        logger.error(f"Error getting access log: {str(e)}")
        return jsonify({'success': False, 'message': f'获取访问记录失败: {str(e)}'}), 500

@app.route('/api/total-visits')
def get_total_visits():
    """获取总访问次数API（所有用户可访问）"""
    try:
        access_data = load_access_log()

        response = jsonify({
            'success': True,
            'total_visits': access_data['total_visits']
        })

        # 禁用缓存
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        return response

    except Exception as e:
        logger.error(f"Error getting total visits: {str(e)}")
        return jsonify({'success': False, 'total_visits': 0})

@app.route('/api/mermindfig/gen_question/gen_questions.html')
def serve_gen_questions_html():
    """提供题目生成器HTML页面"""
    try:
        return send_from_directory(
            os.path.join(current_dir, 'mermindfig', 'gen_question'),
            'gen_questions.html'
        )
    except FileNotFoundError:
        return "题目生成器页面未找到", 404

@app.route('/favicon.ico')
def favicon():
    """处理favicon请求"""
    try:
        # 尝试从main/static目录提供favicon
        return send_from_directory(
            os.path.join(current_dir, 'main', 'static'),
            'favicon.ico',
            mimetype='image/vnd.microsoft.icon'
        )
    except FileNotFoundError:
        # 如果文件不存在，返回204 No Content状态码
        return '', 204

# 初始化主应用并注册蓝图和dashboard API路由
init_app(app)
app.register_blueprint(main_bp, url_prefix='/main')


if __name__ == '__main__':
    try:
        # 创建必要的目录
        reset_user_counts()
        logger.info('Starting server...')
        
        # 使用自定义的 TransLogger
        wsgi_app = CustomTransLogger(app)
        
        # 使用请求上下文来存储计时信息
        @app.before_request
        def before_request():
            request_id = str(uuid.uuid4())[:8]
            request.start_time = time.time()
            request.request_id = request_id
            logger.info(
                f"Received request: {request.method} {request.path}",
                extra={'request_id': request_id}
            )

        @app.after_request
        def after_request(response):
            start_time = getattr(request, 'start_time', time.time())
            request_id = getattr(request, 'request_id', 'unknown')
            
            duration = (time.time() - start_time) * 1000
            logger.info(
                f"Completed {request.method} {request.path} "
                f"with status {response.status_code} in {duration:.2f}ms",
                extra={'request_id': request_id}
            )
            return response
        
        serve(
            wsgi_app,
            host='0.0.0.0',
            port=5015,
            threads=32,  # 增加线程数
            channel_timeout=36000,  # 增加通道超时时间到10小时（36000秒）
            cleanup_interval=300,  # 增加清理间隔到5分钟
            connection_limit=2000,  # 增加连接限制
            max_request_header_size=5242880,  # 增加请求头大小限制到5MB
            max_request_body_size=1073741824,  # 保持请求体大小限制为1GB
            url_scheme='http',
            ident='VideoServer/1.0'
        )
    except Exception as e:
        logger.error(f'Server failed to start: {str(e)}')
        raise